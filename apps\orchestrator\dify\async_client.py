"""
Dify SDK Async Client

Asynchronous client for the Dify API providing high-performance
non-blocking operations for chat, completion, and workflow endpoints.
"""

import asyncio
import logging
from typing import Optional, Dict, Any, AsyncIterator, Union, List, TYPE_CHECKING
import httpx
# from urllib.parse import urljoin  # Not needed anymore

from .config import DifyConfig, default_config
from .exceptions import (
    DifyError,
    DifyAPIError,
    DifyAuthenticationError,
    DifyConnectionError,
    DifyTimeoutError,
    DifyStreamingError,
    DifyValidationError,
    create_exception_from_response,
)
from .models.common import ResponseMode
from .utils.validation import validate_api_key

# Type checking imports to avoid circular imports
if TYPE_CHECKING:
    from .models.chat import (
        ChatResponse, ChatStreamEvent, StopChatResponse,
        SuggestedQuestionsResponse, ChatFeedbackResponse
    )
    from .models.completion import (
        CompletionResponse, CompletionStreamEvent, CompletionStopResponse,
        CompletionFeedbackResponse
    )
    from .models.workflow import (
        WorkflowResponse, WorkflowStreamEvent, WorkflowStopResponse,
        WorkflowRunListResponse
    )
    from .models.files import (
        FileUploadResponse, FileListResponse, FileDeleteResponse,
        FileInfoResponse, FileDownloadResponse
    )
    from .models.conversations import (
        ConversationListResponse, ConversationMessagesResponse,
        ConversationRenameResponse, ConversationDeleteResponse,
        ConversationPinResponse
    )


class AsyncDifyClient:
    """
    Asynchronous client for the Dify API.

    This client provides non-blocking operations for all Dify API endpoints,
    making it suitable for high-performance applications and concurrent processing.

    Example:
        async with AsyncDifyClient(api_key="your-api-key") as client:
            response = await client.send_chat_message("Hello, how are you?", user="user-123")
            print(response.answer)
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        config: Optional[DifyConfig] = None,
        logger: Optional[logging.Logger] = None,
        **kwargs
    ):
        """
        Initialize the async Dify client.

        Args:
            api_key: Dify API key. If not provided, will use config or environment variable.
            base_url: Base URL for the Dify API. If not provided, will use config default.
            config: DifyConfig instance. If not provided, will use default config.
            logger: Logger instance. If not provided, will create a default logger.
            **kwargs: Additional configuration overrides.

        Raises:
            DifyAuthenticationError: If no valid API key is provided.
            DifyValidationError: If configuration is invalid.
        """
        # Set up configuration
        self.config = config or default_config

        # Override config with provided parameters
        if api_key:
            self.api_key = api_key
        elif self.config.api_key:
            self.api_key = self.config.api_key
        else:
            raise DifyAuthenticationError("No API key provided. Set api_key parameter or DIFY_API_KEY environment variable.")

        # Validate API key format
        validate_api_key(self.api_key)

        self.base_url = base_url or self.config.base_url

        # Apply any additional config overrides
        if kwargs:
            config_dict = self.config.model_dump()
            config_dict.update(kwargs)
            self.config = DifyConfig(**config_dict)

        # Set up logging
        self.logger = logger or logging.getLogger(__name__)

        # Initialize async HTTP client (will be created when needed)
        self.http_client: Optional[httpx.AsyncClient] = None
        self._client_initialized = False

    async def _ensure_client_initialized(self) -> None:
        """Ensure the HTTP client is initialized."""
        if not self._client_initialized:
            await self._init_http_client()
            self._client_initialized = True

    async def _init_http_client(self) -> None:
        """Initialize the async HTTP client with appropriate configuration."""
        timeout = httpx.Timeout(
            connect=self.config.connect_timeout,
            read=self.config.read_timeout,
            write=self.config.timeout,
            pool=self.config.timeout
        )

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": self.config.user_agent,
        }

        self.http_client = httpx.AsyncClient(
            base_url=self.base_url,
            headers=headers,
            timeout=timeout,
            follow_redirects=True,
        )

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_client_initialized()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Parameters are required by the protocol but not used
        _ = exc_type, exc_val, exc_tb
        await self.close()

    async def close(self):
        """Close the async client and clean up resources."""
        if self.http_client:
            await self.http_client.aclose()
            self.http_client = None
            self._client_initialized = False

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        stream: bool = False,
    ) -> Union[httpx.Response, AsyncIterator[str]]:
        """
        Make an async HTTP request to the Dify API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            data: JSON data for the request body
            params: Query parameters
            files: Files for multipart upload
            stream: Whether to stream the response

        Returns:
            HTTP response or async streaming iterator

        Raises:
            DifyAPIError: For API-related errors
            DifyConnectionError: For connection errors
            DifyTimeoutError: For timeout errors
        """
        await self._ensure_client_initialized()

        # Fix URL construction - urljoin with leading slash replaces the path
        if endpoint.startswith('http'):
            url = endpoint
        else:
            # Ensure base_url ends with / and endpoint doesn't start with /
            base = self.base_url.rstrip('/')
            endpoint_clean = endpoint.lstrip('/')
            url = f"{base}/{endpoint_clean}"

        # Log request if enabled
        if self.config.log_requests:
            self.logger.info(f"Making async {method} request to {url}")
            if data and self.config.log_responses:
                # Log data but mask sensitive information
                safe_data = {k: v for k, v in data.items() if k not in ['api_key', 'token']}
                self.logger.debug(f"Request data: {safe_data}")

        try:
            # Prepare request arguments
            request_kwargs = {
                "method": method,
                "url": url,
                "params": params,
            }

            # Add streaming headers if needed
            if stream:
                headers = {"Accept": "text/event-stream"}
                request_kwargs["headers"] = headers

            if files:
                request_kwargs["files"] = files
                if data:
                    request_kwargs["data"] = data
            elif data:
                request_kwargs["json"] = data

            # Make the async request
            response = await self.http_client.request(**request_kwargs)

            # Log response if enabled
            if self.config.log_responses:
                self.logger.info(f"Async response status: {response.status_code}")
                if response.status_code >= 400:
                    self.logger.error(f"Error response: {response.text}")

            # Handle streaming responses
            if stream and response.status_code == 200:
                return self._handle_async_streaming_response(response)

            # Handle non-streaming responses
            self._check_response_status(response)
            return response

        except httpx.ConnectError as e:
            raise DifyConnectionError(f"Failed to connect to Dify API: {e}")
        except httpx.TimeoutException as e:
            raise DifyTimeoutError(f"Request timed out: {e}", timeout=self.config.timeout)
        except httpx.RequestError as e:
            raise DifyConnectionError(f"Request error: {e}")

    def _check_response_status(self, response: httpx.Response) -> None:
        """
        Check response status and raise appropriate exceptions for errors.

        Args:
            response: HTTP response object

        Raises:
            DifyAPIError: For API errors (4xx, 5xx status codes)
        """
        if response.status_code >= 400:
            try:
                error_data = response.json()
            except Exception:
                error_data = {"message": response.text or "Unknown error"}

            request_id = response.headers.get("X-Request-ID")
            exception = create_exception_from_response(
                response.status_code, error_data, request_id
            )
            raise exception

    async def _handle_async_streaming_response(self, response: httpx.Response) -> AsyncIterator[str]:
        """
        Handle async streaming Server-Sent Events (SSE) responses.

        Args:
            response: HTTP response object with streaming content

        Yields:
            Individual SSE events as strings
        """
        try:
            buffer = ""
            async for chunk in response.aiter_bytes(chunk_size=1024):
                if chunk:
                    # Decode chunk and add to buffer
                    try:
                        text = chunk.decode('utf-8')
                        buffer += text
                    except UnicodeDecodeError:
                        # Handle partial UTF-8 sequences
                        continue

                    # Process complete lines
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()

                        if line.startswith("data: "):
                            data = line[6:]  # Remove "data: " prefix
                            if data.strip() == "[DONE]":
                                return
                            elif data.strip():  # Skip empty data lines
                                yield data
                        elif line.startswith("event: "):
                            # Handle event type lines if needed
                            continue
                        elif line.startswith(": "):
                            # Handle comment lines (ignore)
                            continue

        except httpx.ReadTimeout as e:
            raise DifyTimeoutError(f"Async streaming response timed out: {e}")
        except httpx.RequestError as e:
            raise DifyConnectionError(f"Connection error during async streaming: {e}")
        except Exception as e:
            raise DifyStreamingError(f"Error processing async streaming response: {e}")

    # Async Chat endpoints
    async def send_chat_message(
        self,
        query: str,
        user: str,
        conversation_id: Optional[str] = None,
        inputs: Optional[Dict[str, Any]] = None,
        response_mode: ResponseMode = ResponseMode.BLOCKING,
        conversation_variables: Optional[List[Any]] = None,
        files: Optional[List[Dict[str, Any]]] = None,
        auto_generate_name: bool = True,
    ) -> Union['ChatResponse', AsyncIterator['ChatStreamEvent']]:
        """
        Send a chat message to the Dify application asynchronously.

        Args:
            query: User's chat message/query
            user: User identifier
            conversation_id: Optional conversation ID for continuing a conversation
            inputs: Optional input variables for the chat application
            response_mode: Response mode (blocking or streaming)
            conversation_variables: Optional session-specific variables
            files: Optional files to include with the message
            auto_generate_name: Whether to auto-generate conversation name

        Returns:
            ChatResponse for blocking mode, AsyncIterator of ChatStreamEvent for streaming

        Raises:
            DifyAPIError: For API-related errors
            DifyValidationError: For validation errors
        """
        from .models.chat import ChatRequest, ChatResponse
        from .utils.validation import sanitize_input, validate_inputs_dict

        # Validate and sanitize inputs
        query = sanitize_input(query)
        inputs = validate_inputs_dict(inputs or {})

        # Create request
        request_data = ChatRequest(
            query=query,
            user=user,
            conversation_id=conversation_id,
            inputs=inputs,
            response_mode=response_mode,
            conversation_variables=conversation_variables,
            files=files,
            auto_generate_name=auto_generate_name,
        )

        # Make API request
        if response_mode == ResponseMode.STREAMING:
            response = await self._make_request(
                "POST",
                "/chat-messages",
                data=request_data.model_dump(exclude_none=True),
                stream=True
            )
            return self._parse_async_chat_stream(response)
        else:
            response = await self._make_request(
                "POST",
                "/chat-messages",
                data=request_data.model_dump(exclude_none=True)
            )
            return ChatResponse(**response.json())

    async def _parse_async_chat_stream(self, response_stream: AsyncIterator[str]) -> AsyncIterator['ChatStreamEvent']:
        """
        Parse async streaming chat response into ChatStreamEvent objects.

        Args:
            response_stream: AsyncIterator of raw SSE event strings

        Yields:
            ChatStreamEvent objects
        """
        from .models.chat import parse_chat_stream_event

        async for event_data in response_stream:
            event = parse_chat_stream_event(event_data)
            if event:
                yield event

    async def stop_chat_message(self, task_id: str, user: str) -> 'StopChatResponse':
        """
        Stop a chat message generation asynchronously.

        Args:
            task_id: Task ID of the chat message to stop
            user: User identifier

        Returns:
            StopChatResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.chat import StopChatRequest, StopChatResponse

        request_data = StopChatRequest(task_id=task_id, user=user)

        response = await self._make_request(
            "POST",
            f"/chat-messages/{task_id}/stop",
            data=request_data.model_dump()
        )

        return StopChatResponse(**response.json())

    async def get_suggested_questions(self, message_id: str, user: str) -> 'SuggestedQuestionsResponse':
        """
        Get suggested questions for a chat message asynchronously.

        Args:
            message_id: Message ID to get suggestions for
            user: User identifier

        Returns:
            SuggestedQuestionsResponse with suggested questions

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.chat import SuggestedQuestionsRequest, SuggestedQuestionsResponse

        request_data = SuggestedQuestionsRequest(message_id=message_id, user=user)

        response = await self._make_request(
            "GET",
            f"/messages/{message_id}/suggested",
            params=request_data.model_dump()
        )

        return SuggestedQuestionsResponse(**response.json())

    async def send_chat_feedback(
        self,
        message_id: str,
        rating: str,
        user: str,
        content: Optional[str] = None
    ) -> 'ChatFeedbackResponse':
        """
        Send feedback for a chat message asynchronously.

        Args:
            message_id: Message ID to provide feedback for
            rating: Feedback rating ('like' or 'dislike')
            user: User identifier
            content: Optional feedback content/comment

        Returns:
            ChatFeedbackResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.chat import ChatFeedbackRequest, ChatFeedbackResponse

        request_data = ChatFeedbackRequest(
            message_id=message_id,
            rating=rating,
            user=user,
            content=content
        )

        response = await self._make_request(
            "POST",
            f"/messages/{message_id}/feedbacks",
            data=request_data.model_dump(exclude_none=True)
        )

        return ChatFeedbackResponse(**response.json())

    # Async Completion endpoints
    async def create_completion(
        self,
        inputs: Dict[str, Any],
        user: str,
        response_mode: ResponseMode = ResponseMode.BLOCKING,
        files: Optional[List[Dict[str, Any]]] = None,
    ) -> Union['CompletionResponse', AsyncIterator['CompletionStreamEvent']]:
        """
        Create a text completion using the Dify application asynchronously.

        Args:
            inputs: Input variables for the completion application
            user: User identifier
            response_mode: Response mode (blocking or streaming)
            files: Optional files to include with the completion request

        Returns:
            CompletionResponse for blocking mode, AsyncIterator of CompletionStreamEvent for streaming

        Raises:
            DifyAPIError: For API-related errors
            DifyValidationError: For validation errors
        """
        from .models.completion import CompletionRequest, CompletionResponse
        from .utils.validation import validate_inputs_dict

        # Validate and sanitize inputs
        inputs = validate_inputs_dict(inputs)

        # Create request
        request_data = CompletionRequest(
            inputs=inputs,
            user=user,
            response_mode=response_mode,
            files=files,
        )

        # Make API request
        if response_mode == ResponseMode.STREAMING:
            response = await self._make_request(
                "POST",
                "/completion-messages",
                data=request_data.model_dump(exclude_none=True),
                stream=True
            )
            return self._parse_async_completion_stream(response)
        else:
            response = await self._make_request(
                "POST",
                "/completion-messages",
                data=request_data.model_dump(exclude_none=True)
            )
            return CompletionResponse(**response.json())

    async def _parse_async_completion_stream(self, response_stream: AsyncIterator[str]) -> AsyncIterator['CompletionStreamEvent']:
        """
        Parse async streaming completion response into CompletionStreamEvent objects.

        Args:
            response_stream: AsyncIterator of raw SSE event strings

        Yields:
            CompletionStreamEvent objects
        """
        from .models.completion import parse_completion_stream_event

        async for event_data in response_stream:
            event = parse_completion_stream_event(event_data)
            if event:
                yield event

    async def stop_completion(self, task_id: str, user: str) -> 'CompletionStopResponse':
        """
        Stop a completion generation asynchronously.

        Args:
            task_id: Task ID of the completion to stop
            user: User identifier

        Returns:
            CompletionStopResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.completion import CompletionStopRequest, CompletionStopResponse

        request_data = CompletionStopRequest(task_id=task_id, user=user)

        response = await self._make_request(
            "POST",
            f"/completion-messages/{task_id}/stop",
            data=request_data.model_dump()
        )

        return CompletionStopResponse(**response.json())

    async def send_completion_feedback(
        self,
        message_id: str,
        rating: str,
        user: str,
        content: Optional[str] = None
    ) -> 'CompletionFeedbackResponse':
        """
        Send feedback for a completion message asynchronously.

        Args:
            message_id: Message ID to provide feedback for
            rating: Feedback rating ('like' or 'dislike')
            user: User identifier
            content: Optional feedback content/comment

        Returns:
            CompletionFeedbackResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.completion import CompletionFeedbackRequest, CompletionFeedbackResponse

        request_data = CompletionFeedbackRequest(
            message_id=message_id,
            rating=rating,
            user=user,
            content=content
        )

        response = await self._make_request(
            "POST",
            f"/messages/{message_id}/feedbacks",
            data=request_data.model_dump(exclude_none=True)
        )

        return CompletionFeedbackResponse(**response.json())

    # Async Workflow endpoints
    async def run_workflow(
        self,
        inputs: Dict[str, Any],
        user: str,
        response_mode: ResponseMode = ResponseMode.BLOCKING,
        files: Optional[List[Dict[str, Any]]] = None,
    ) -> Union['WorkflowResponse', AsyncIterator['WorkflowStreamEvent']]:
        """
        Execute a workflow using the Dify application asynchronously.

        Args:
            inputs: Input variables for the workflow
            user: User identifier
            response_mode: Response mode (blocking or streaming)
            files: Optional files to include with the workflow execution

        Returns:
            WorkflowResponse for blocking mode, AsyncIterator of WorkflowStreamEvent for streaming

        Raises:
            DifyAPIError: For API-related errors
            DifyValidationError: For validation errors
        """
        from .models.workflow import WorkflowRequest, WorkflowResponse
        from .utils.validation import validate_inputs_dict

        # Validate and sanitize inputs
        inputs = validate_inputs_dict(inputs)

        # Create request
        request_data = WorkflowRequest(
            inputs=inputs,
            user=user,
            response_mode=response_mode,
            files=files,
        )

        # Make API request
        if response_mode == ResponseMode.STREAMING:
            response = await self._make_request(
                "POST",
                "/workflows/run",
                data=request_data.model_dump(exclude_none=True),
                stream=True
            )
            return self._parse_async_workflow_stream(response)
        else:
            response = await self._make_request(
                "POST",
                "/workflows/run",
                data=request_data.model_dump(exclude_none=True)
            )
            return WorkflowResponse(**response.json())

    async def _parse_async_workflow_stream(self, response_stream: AsyncIterator[str]) -> AsyncIterator['WorkflowStreamEvent']:
        """
        Parse async streaming workflow response into WorkflowStreamEvent objects.

        Args:
            response_stream: AsyncIterator of raw SSE event strings

        Yields:
            WorkflowStreamEvent objects
        """
        from .models.workflow import parse_workflow_stream_event

        async for event_data in response_stream:
            event = parse_workflow_stream_event(event_data)
            if event:
                yield event

    async def stop_workflow(self, task_id: str, user: str) -> 'WorkflowStopResponse':
        """
        Stop a workflow execution asynchronously.

        Args:
            task_id: Task ID of the workflow to stop
            user: User identifier

        Returns:
            WorkflowStopResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.workflow import WorkflowStopRequest, WorkflowStopResponse

        request_data = WorkflowStopRequest(task_id=task_id, user=user)

        response = await self._make_request(
            "POST",
            f"/workflows/{task_id}/stop",
            data=request_data.model_dump()
        )

        return WorkflowStopResponse(**response.json())

    async def get_workflow_runs(
        self,
        user: str,
        limit: int = 20,
        offset: int = 0
    ) -> 'WorkflowRunListResponse':
        """
        Get a list of workflow runs for a user asynchronously.

        Args:
            user: User identifier
            limit: Number of runs to return (1-100)
            offset: Number of runs to skip

        Returns:
            WorkflowRunListResponse with the list of runs

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.workflow import WorkflowRunListRequest, WorkflowRunListResponse

        request_data = WorkflowRunListRequest(
            user=user,
            limit=limit,
            offset=offset
        )

        response = await self._make_request(
            "GET",
            "/workflows/runs",
            params=request_data.model_dump()
        )

        return WorkflowRunListResponse(**response.json())

    # Async File management endpoints
    async def upload_file(
        self,
        file_path: str,
        user: str,
        file_name: Optional[str] = None
    ) -> 'FileUploadResponse':
        """
        Upload a file to the Dify application asynchronously.

        Args:
            file_path: Path to the file to upload
            user: User identifier
            file_name: Optional custom file name (defaults to basename of file_path)

        Returns:
            FileUploadResponse with file information

        Raises:
            DifyAPIError: For API-related errors
            DifyValidationError: For validation errors
            FileNotFoundError: If the file doesn't exist
        """
        from .models.files import FileUploadRequest, FileUploadResponse, validate_file_for_upload
        import os

        # Validate file
        validate_file_for_upload(file_path)

        # Determine file name
        if not file_name:
            file_name = os.path.basename(file_path)

        # Create request
        request_data = FileUploadRequest(
            user=user,
            file_name=file_name,
            file_path=file_path
        )

        # Prepare file for upload
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()

            files = {
                'file': (file_name, file_content, request_data.get_mime_type())
            }

            response = await self._make_request(
                "POST",
                "/files/upload",
                data={'user': user},
                files=files
            )

            return FileUploadResponse(**response.json())

        except FileNotFoundError:
            raise DifyValidationError(f"File not found: {file_path}")
        except PermissionError:
            raise DifyValidationError(f"Permission denied accessing file: {file_path}")
        except OSError as e:
            raise DifyValidationError(f"Error reading file {file_path}: {e}")

    async def list_files(
        self,
        user: str,
        limit: int = 20,
        offset: int = 0,
        search: Optional[str] = None,
        extension: Optional[str] = None
    ) -> 'FileListResponse':
        """
        List files for a user asynchronously.

        Args:
            user: User identifier
            limit: Number of files to return (1-100)
            offset: Number of files to skip
            search: Optional search query for file names
            extension: Optional filter by file extension

        Returns:
            FileListResponse with the list of files

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.files import FileListRequest, FileListResponse

        request_data = FileListRequest(
            user=user,
            limit=limit,
            offset=offset,
            search=search,
            extension=extension
        )

        response = await self._make_request(
            "GET",
            "/files",
            params=request_data.model_dump(exclude_none=True)
        )

        return FileListResponse(**response.json())

    async def delete_file(self, file_id: str, user: str) -> 'FileDeleteResponse':
        """
        Delete a file asynchronously.

        Args:
            file_id: File ID to delete
            user: User identifier

        Returns:
            FileDeleteResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.files import FileDeleteRequest, FileDeleteResponse

        request_data = FileDeleteRequest(file_id=file_id, user=user)

        response = await self._make_request(
            "DELETE",
            f"/files/{file_id}",
            params=request_data.model_dump()
        )

        return FileDeleteResponse(**response.json())

    async def get_file_info(self, file_id: str, user: str) -> 'FileInfoResponse':
        """
        Get information about a file asynchronously.

        Args:
            file_id: File ID
            user: User identifier

        Returns:
            FileInfoResponse with file information

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.files import FileInfoRequest, FileInfoResponse

        request_data = FileInfoRequest(file_id=file_id, user=user)

        response = await self._make_request(
            "GET",
            f"/files/{file_id}",
            params=request_data.model_dump()
        )

        return FileInfoResponse(**response.json())

    async def download_file(self, file_id: str, user: str, save_path: Optional[str] = None) -> 'FileDownloadResponse':
        """
        Download a file asynchronously.

        Args:
            file_id: File ID to download
            user: User identifier
            save_path: Optional path to save the file (if not provided, returns file content)

        Returns:
            FileDownloadResponse with file content and metadata

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.files import FileDownloadRequest, FileDownloadResponse

        request_data = FileDownloadRequest(file_id=file_id, user=user)

        response = await self._make_request(
            "GET",
            f"/files/{file_id}/download",
            params=request_data.model_dump()
        )

        # Get file content and metadata
        file_content = response.content
        file_name = response.headers.get('Content-Disposition', '').split('filename=')[-1].strip('"')
        mime_type = response.headers.get('Content-Type', 'application/octet-stream')
        size = len(file_content)

        # Save file if path provided
        if save_path:
            try:
                with open(save_path, 'wb') as f:
                    f.write(file_content)
            except OSError as e:
                raise DifyValidationError(f"Error saving file to {save_path}: {e}")

        return FileDownloadResponse(
            file_content=file_content,
            file_name=file_name or f"file_{file_id}",
            mime_type=mime_type,
            size=size
        )

    # Async Conversation management endpoints
    async def get_conversations(
        self,
        user: str,
        limit: int = 20,
        offset: int = 0,
        pinned: Optional[bool] = None
    ) -> 'ConversationListResponse':
        """
        Get a list of conversations for a user asynchronously.

        Args:
            user: User identifier
            limit: Number of conversations to return (1-100)
            offset: Number of conversations to skip
            pinned: Optional filter by pinned status

        Returns:
            ConversationListResponse with the list of conversations

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.conversations import ConversationListRequest, ConversationListResponse

        request_data = ConversationListRequest(
            user=user,
            limit=limit,
            offset=offset,
            pinned=pinned
        )

        response = await self._make_request(
            "GET",
            "/conversations",
            params=request_data.model_dump(exclude_none=True)
        )

        return ConversationListResponse(**response.json())

    async def get_conversation_messages(
        self,
        conversation_id: str,
        user: str,
        limit: int = 20,
        offset: int = 0
    ) -> 'ConversationMessagesResponse':
        """
        Get messages from a conversation asynchronously.

        Args:
            conversation_id: Conversation ID
            user: User identifier
            limit: Number of messages to return (1-100)
            offset: Number of messages to skip

        Returns:
            ConversationMessagesResponse with the list of messages

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.conversations import ConversationMessagesRequest, ConversationMessagesResponse

        request_data = ConversationMessagesRequest(
            conversation_id=conversation_id,
            user=user,
            limit=limit,
            offset=offset
        )

        response = await self._make_request(
            "GET",
            f"/conversations/{conversation_id}/messages",
            params=request_data.model_dump()
        )

        return ConversationMessagesResponse(**response.json())

    async def rename_conversation(
        self,
        conversation_id: str,
        name: str,
        user: str,
        auto_generate: bool = False
    ) -> 'ConversationRenameResponse':
        """
        Rename a conversation asynchronously.

        Args:
            conversation_id: Conversation ID
            name: New conversation name
            user: User identifier
            auto_generate: Whether to auto-generate the name

        Returns:
            ConversationRenameResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.conversations import ConversationRenameRequest, ConversationRenameResponse

        request_data = ConversationRenameRequest(
            conversation_id=conversation_id,
            name=name,
            user=user,
            auto_generate=auto_generate
        )

        response = await self._make_request(
            "POST",
            f"/conversations/{conversation_id}/name",
            data=request_data.model_dump()
        )

        return ConversationRenameResponse(**response.json())

    async def delete_conversation(self, conversation_id: str, user: str) -> 'ConversationDeleteResponse':
        """
        Delete a conversation asynchronously.

        Args:
            conversation_id: Conversation ID
            user: User identifier

        Returns:
            ConversationDeleteResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.conversations import ConversationDeleteRequest, ConversationDeleteResponse

        request_data = ConversationDeleteRequest(
            conversation_id=conversation_id,
            user=user
        )

        response = await self._make_request(
            "DELETE",
            f"/conversations/{conversation_id}",
            params=request_data.model_dump()
        )

        return ConversationDeleteResponse(**response.json())

    async def pin_conversation(
        self,
        conversation_id: str,
        user: str,
        pinned: bool = True
    ) -> 'ConversationPinResponse':
        """
        Pin or unpin a conversation asynchronously.

        Args:
            conversation_id: Conversation ID
            user: User identifier
            pinned: Whether to pin the conversation

        Returns:
            ConversationPinResponse with the result

        Raises:
            DifyAPIError: For API-related errors
        """
        from .models.conversations import ConversationPinRequest, ConversationPinResponse

        request_data = ConversationPinRequest(
            conversation_id=conversation_id,
            user=user,
            pinned=pinned
        )

        response = await self._make_request(
            "POST",
            f"/conversations/{conversation_id}/pin",
            data=request_data.model_dump()
        )

        return ConversationPinResponse(**response.json())

    # Health check method
    async def health_check(self) -> bool:
        """
        Perform an async health check to verify API connectivity.

        Returns:
            True if the API is accessible, False otherwise
        """
        try:
            # Make a simple request to check connectivity
            response = await self._make_request("GET", "/health")
            return response.status_code == 200
        except Exception as e:
            self.logger.warning(f"Async health check failed: {e}")
            return False
