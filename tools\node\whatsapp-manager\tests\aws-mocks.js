// AWS SDK Mocks for Jest Testing
// This file mocks all AWS SDK modules to prevent real AWS API calls during testing

// Mock DynamoDB Client
jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: jest.fn().mockResolvedValue({}),
    destroy: jest.fn().mockResolvedValue(undefined),
  })),
  CreateTableCommand: jest.fn(),
  DescribeTableCommand: jest.fn(),
  DeleteTableCommand: jest.fn(),
  ListTablesCommand: jest.fn(),
  UpdateTableCommand: jest.fn(),
  PutItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn(),
  QueryCommand: jest.fn(),
  ScanCommand: jest.fn(),
  BatchWriteItemCommand: jest.fn(),
  BatchGetItemCommand: jest.fn(),
}));

// Mock DynamoDB Document Client
jest.mock('@aws-sdk/lib-dynamodb', () => ({
  DynamoDBDocumentClient: {
    from: jest.fn().mockImplementation(() => ({
      send: jest.fn().mockImplementation((command) => {
        // Return appropriate mock responses based on command type
        const commandName = command.constructor.name;
        
        switch (commandName) {
          case 'GetCommand':
            return Promise.resolve({ Item: null });
          case 'PutCommand':
            return Promise.resolve({});
          case 'UpdateCommand':
            return Promise.resolve({});
          case 'DeleteCommand':
            return Promise.resolve({});
          case 'QueryCommand':
            return Promise.resolve({ Items: [], Count: 0, ScannedCount: 0 });
          case 'ScanCommand':
            return Promise.resolve({ Items: [], Count: 0, ScannedCount: 0 });
          case 'BatchWriteCommand':
            return Promise.resolve({ UnprocessedItems: {} });
          case 'BatchGetCommand':
            return Promise.resolve({ Responses: {} });
          default:
            return Promise.resolve({});
        }
      }),
      destroy: jest.fn().mockResolvedValue(undefined),
    })),
  },
  PutCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  GetCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  UpdateCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  DeleteCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  QueryCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  ScanCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  BatchWriteCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  BatchGetCommand: jest.fn().mockImplementation((params) => ({ input: params })),
}));

// Mock SSM (Parameter Store) Client
jest.mock('@aws-sdk/client-ssm', () => ({
  SSMClient: jest.fn().mockImplementation(() => ({
    send: jest.fn().mockImplementation((command) => {
      const commandName = command.constructor.name;

      switch (commandName) {
        case 'GetParameterCommand':
          const parameterName = command.input.Name;

          // Return different mock parameters based on parameter name
          if (parameterName.includes('jwt') || parameterName.includes('JWT')) {
            return Promise.resolve({
              Parameter: {
                Name: parameterName,
                Value: 'mock-jwt-secret-32-characters-long-for-testing',
                Type: 'SecureString',
                Version: 1,
              },
            });
          }

          if (parameterName.includes('database') || parameterName.includes('DB')) {
            return Promise.resolve({
              Parameter: {
                Name: parameterName,
                Value: JSON.stringify({
                  host: 'localhost',
                  port: 5432,
                  database: 'test-db',
                  username: 'test-user',
                  password: 'test-password'
                }),
                Type: 'SecureString',
                Version: 1,
              },
            });
          }

          // Default mock parameter
          return Promise.resolve({
            Parameter: {
              Name: parameterName,
              Value: 'mock-parameter-value',
              Type: 'String',
              Version: 1,
            },
          });

        case 'PutParameterCommand':
          return Promise.resolve({
            Version: 1,
            Tier: 'Standard',
          });

        case 'DeleteParameterCommand':
          return Promise.resolve({});

        case 'GetParametersCommand':
          return Promise.resolve({
            Parameters: [
              {
                Name: '/test/parameter1',
                Value: 'value1',
                Type: 'String',
                Version: 1,
              },
              {
                Name: '/test/parameter2',
                Value: 'value2',
                Type: 'SecureString',
                Version: 1,
              }
            ],
            InvalidParameters: [],
          });

        default:
          return Promise.resolve({});
      }
    }),
    destroy: jest.fn().mockResolvedValue(undefined),
  })),
  GetParameterCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  PutParameterCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  DeleteParameterCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  GetParametersCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  GetParametersByPathCommand: jest.fn().mockImplementation((params) => ({ input: params })),
}));

// Note: Only mock AWS services that are actually installed as dependencies
// Current project only uses: @aws-sdk/client-dynamodb, @aws-sdk/lib-dynamodb, @aws-sdk/client-ssm

// Global test utilities for AWS mocks
global.mockAWSResponse = (service, method, response) => {
  const mockImplementation = jest.fn().mockResolvedValue(response);

  switch (service) {
    case 'dynamodb':
      const { DynamoDBDocumentClient } = require('@aws-sdk/lib-dynamodb');
      const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
      const client = new DynamoDBClient({});
      const docClient = DynamoDBDocumentClient.from(client);
      docClient.send.mockImplementation((command) => {
        if (command.constructor.name === method) {
          return mockImplementation(command);
        }
        return Promise.resolve({});
      });
      break;
    case 'ssm':
      const { SSMClient } = require('@aws-sdk/client-ssm');
      const ssmClient = new SSMClient({});
      ssmClient.send.mockImplementation((command) => {
        if (command.constructor.name === method) {
          return mockImplementation(command);
        }
        return Promise.resolve({});
      });
      break;
  }
};

global.resetAWSMocks = () => {
  jest.clearAllMocks();
};

// Verify no real AWS calls are made
global.verifyNoAWSCalls = () => {
  // This would be implemented to check that no real AWS endpoints were called
  // For now, we rely on the mocks to prevent real calls
  return true;
};

console.log('✅ AWS SDK mocks initialized - all AWS calls will be intercepted');
