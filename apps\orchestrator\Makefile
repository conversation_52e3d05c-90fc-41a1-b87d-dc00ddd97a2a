# Makefile for Orchestrator Lambda

.PHONY: help install test lint format build deploy clean

# Variables
PYTHON := python3.12
PIP := pip
DOCKER := docker
ECR_REGISTRY := 381491882604.dkr.ecr.ap-southeast-5.amazonaws.com
IMAGE_NAME := lambda-python-orchestrator
AWS_REGION := ap-southeast-5

help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## Install Python dependencies
	$(PIP) install -r requirements.txt

install-dev: ## Install development dependencies
	$(PIP) install -r requirements.txt
	$(PIP) install -e .

test: ## Run tests
	powershell -Command "$$env:PYTHONPATH='src;.'; pytest"

test-coverage: ## Run tests with coverage
	powershell -Command "$$env:PYTHONPATH='src;.'; pytest --cov=src --cov-report=term-missing --cov-report=html"

test-unit: ## Run unit tests only
	powershell -Command "$$env:PYTHONPATH='src;.'; pytest tests/unit/"

test-integration: ## Run integration tests only
	powershell -Command "$$env:PYTHONPATH='src;.'; pytest tests/integration/"

lint: ## Run linting
	flake8 src/ tests/
	mypy src/

format: ## Format code
	black src/ tests/

format-check: ## Check code formatting
	black --check src/ tests/

build: ## Build Docker image
	$(DOCKER) build -t $(IMAGE_NAME) .

build-prod: ## Build production Docker image
	$(DOCKER) build -t $(IMAGE_NAME):latest .
	$(DOCKER) tag $(IMAGE_NAME):latest $(ECR_REGISTRY)/$(IMAGE_NAME):latest

push: ## Push image to ECR
	aws ecr get-login-password --region $(AWS_REGION) | $(DOCKER) login --username AWS --password-stdin $(ECR_REGISTRY)
	$(DOCKER) push $(ECR_REGISTRY)/$(IMAGE_NAME):latest

deploy: build-prod push ## Build and deploy to ECR
	@echo "Image pushed to $(ECR_REGISTRY)/$(IMAGE_NAME):latest"
	@echo "Update your Lambda function to use this image"

run-local: ## Run locally for testing
	$(DOCKER) run -p 9000:8080 --env-file .env $(IMAGE_NAME)

clean: ## Clean up build artifacts
	$(DOCKER) rmi $(IMAGE_NAME) || true
	$(DOCKER) rmi $(ECR_REGISTRY)/$(IMAGE_NAME):latest || true
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

setup: ## Initial setup for development
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements.txt
	cp .env.example .env
	@echo "Setup complete! Edit .env with your configuration."

check: lint test ## Run all checks (lint + test)

ci: format-check lint test-coverage ## Run CI pipeline checks
