"""
Conversation Models for Dify SDK

Pydantic models for conversation management API requests and responses.
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator

from .common import (
    BaseRequest,
    BaseResponse,
    PaginationRequest,
    PaginationResponse,
)


class ConversationListRequest(PaginationRequest):
    """Request model for listing conversations."""
    
    user: str = Field(description="User identifier")
    first_id: Optional[str] = Field(
        default=None,
        description="First conversation ID for pagination"
    )
    last_id: Optional[str] = Field(
        default=None,
        description="Last conversation ID for pagination"
    )
    pinned: Optional[bool] = Field(
        default=None,
        description="Filter by pinned status"
    )
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()


class ConversationInfo(BaseModel):
    """Information about a conversation."""
    
    id: str = Field(description="Conversation ID")
    name: str = Field(description="Conversation name")
    inputs: Dict[str, Any] = Field(
        default_factory=dict,
        description="Initial conversation inputs"
    )
    status: str = Field(description="Conversation status")
    introduction: Optional[str] = Field(
        default=None,
        description="Conversation introduction"
    )
    created_at: int = Field(description="Creation timestamp")
    updated_at: int = Field(description="Last update timestamp")
    
    class Status:
        NORMAL = "normal"
        PINNED = "pinned"
        ARCHIVED = "archived"


class ConversationListResponse(BaseResponse):
    """Response model for conversation list."""
    
    data: List[ConversationInfo] = Field(
        default_factory=list,
        description="List of conversations"
    )
    has_more: bool = Field(description="Whether there are more conversations")
    limit: int = Field(description="Number of items per page")


class ConversationMessagesRequest(PaginationRequest):
    """Request model for listing conversation messages."""
    
    conversation_id: str = Field(description="Conversation ID")
    user: str = Field(description="User identifier")
    first_id: Optional[str] = Field(
        default=None,
        description="First message ID for pagination"
    )
    last_id: Optional[str] = Field(
        default=None,
        description="Last message ID for pagination"
    )
    
    @field_validator('conversation_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class MessageInfo(BaseModel):
    """Information about a conversation message."""
    
    id: str = Field(description="Message ID")
    conversation_id: str = Field(description="Conversation ID")
    inputs: Dict[str, Any] = Field(
        default_factory=dict,
        description="Message inputs"
    )
    query: str = Field(description="User query")
    answer: str = Field(description="AI response")
    message_files: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Files attached to the message"
    )
    feedback: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Message feedback"
    )
    retriever_resources: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Retrieved resources for RAG"
    )
    created_at: int = Field(description="Creation timestamp")
    
    @field_validator('query', 'answer')
    @classmethod
    def validate_text_fields(cls, v):
        """Ensure text fields are strings."""
        return str(v) if v is not None else ""


class ConversationMessagesResponse(BaseResponse):
    """Response model for conversation messages."""
    
    data: List[MessageInfo] = Field(
        default_factory=list,
        description="List of messages"
    )
    has_more: bool = Field(description="Whether there are more messages")
    limit: int = Field(description="Number of items per page")


class ConversationRenameRequest(BaseRequest):
    """Request model for renaming a conversation."""
    
    conversation_id: str = Field(description="Conversation ID")
    name: str = Field(description="New conversation name")
    user: str = Field(description="User identifier")
    auto_generate: bool = Field(
        default=False,
        description="Whether to auto-generate the name"
    )
    
    @field_validator('conversation_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate conversation name."""
        if not v or not v.strip():
            raise ValueError("Conversation name cannot be empty")
        if len(v.strip()) > 100:
            raise ValueError("Conversation name cannot exceed 100 characters")
        return v.strip()


class ConversationRenameResponse(BaseResponse):
    """Response model for conversation rename."""
    
    result: str = Field(description="Result status")
    name: str = Field(description="Updated conversation name")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"


class ConversationDeleteRequest(BaseRequest):
    """Request model for deleting a conversation."""
    
    conversation_id: str = Field(description="Conversation ID")
    user: str = Field(description="User identifier")
    
    @field_validator('conversation_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class ConversationDeleteResponse(BaseResponse):
    """Response model for conversation deletion."""
    
    result: str = Field(description="Result status")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"


class ConversationPinRequest(BaseRequest):
    """Request model for pinning/unpinning a conversation."""
    
    conversation_id: str = Field(description="Conversation ID")
    user: str = Field(description="User identifier")
    pinned: bool = Field(description="Whether to pin the conversation")
    
    @field_validator('conversation_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class ConversationPinResponse(BaseResponse):
    """Response model for conversation pin/unpin."""
    
    result: str = Field(description="Result status")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"


# Utility functions for conversation models
def create_conversation_list_request(
    user: str,
    limit: int = 20,
    offset: int = 0,
    pinned: Optional[bool] = None,
    **kwargs
) -> ConversationListRequest:
    """
    Create a ConversationListRequest with common parameters.
    
    Args:
        user: User identifier
        limit: Number of conversations to return
        offset: Number of conversations to skip
        pinned: Filter by pinned status
        **kwargs: Additional parameters
        
    Returns:
        ConversationListRequest instance
    """
    return ConversationListRequest(
        user=user,
        limit=limit,
        offset=offset,
        pinned=pinned,
        **kwargs
    )


def create_conversation_messages_request(
    conversation_id: str,
    user: str,
    limit: int = 20,
    offset: int = 0,
    **kwargs
) -> ConversationMessagesRequest:
    """
    Create a ConversationMessagesRequest with common parameters.
    
    Args:
        conversation_id: Conversation ID
        user: User identifier
        limit: Number of messages to return
        offset: Number of messages to skip
        **kwargs: Additional parameters
        
    Returns:
        ConversationMessagesRequest instance
    """
    return ConversationMessagesRequest(
        conversation_id=conversation_id,
        user=user,
        limit=limit,
        offset=offset,
        **kwargs
    )
