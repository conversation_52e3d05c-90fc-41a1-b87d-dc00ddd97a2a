"""
Test basic imports to verify setup.
"""

import pytest
import sys
from pathlib import Path

# Ensure src is in path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Ensure current directory is in path for dify imports
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))


def test_domain_entities_import():
    """Test that domain entities can be imported."""
    from domain.entities import TenantConfig

    # Test basic instantiation
    config = TenantConfig(
        tenant_id="test",
        company_name="Test Company",
        system_instructions="Test instructions",
        product_catalog="Test catalog",
    )

    assert config.tenant_id == "test"
    assert config.company_name == "Test Company"


def test_domain_services_import():
    """Test that domain services can be imported."""
    from domain.services import ConfigurationService

    service = ConfigurationService()
    assert service is not None


def test_agent_request_validation():
    """Test AgentRequest validation works."""
    from domain.entities import AgentRequest

    # Valid request
    request = AgentRequest(
        phone_number="+1234567890", message_content="Test message", message_id="test_msg_001"
    )

    assert request.phone_number == "+1234567890"
    assert request.message_content == "Test message"
    assert request.message_id == "test_msg_001"

    # Test validation errors
    with pytest.raises(ValueError, match="Phone number must be in international format"):
        AgentRequest(
            phone_number="1234567890",  # Missing +
            message_content="Test message",
            message_id="test_msg_001",
        )
