"""
Common Dify SDK Models

Base models and common data structures used across different API endpoints.
"""

from typing import Optional, Dict, Any, List, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from datetime import datetime


class ResponseMode(str, Enum):
    """Response mode for API requests."""
    BLOCKING = "blocking"
    STREAMING = "streaming"


class BaseRequest(BaseModel):
    """Base class for all API requests."""
    
    class Config:
        # Allow extra fields for forward compatibility
        extra = "allow"
        # Use enum values in serialization
        use_enum_values = True


class BaseResponse(BaseModel):
    """Base class for all API responses."""
    
    class Config:
        # Allow extra fields for forward compatibility
        extra = "allow"
        # Use enum values in serialization
        use_enum_values = True


class Usage(BaseModel):
    """Token usage information from API responses."""
    
    prompt_tokens: int = Field(description="Number of tokens in the prompt")
    prompt_unit_price: str = Field(description="Unit price for prompt tokens")
    prompt_price_unit: str = Field(description="Price unit for prompt tokens")
    prompt_price: str = Field(description="Total price for prompt tokens")
    
    completion_tokens: int = Field(description="Number of tokens in the completion")
    completion_unit_price: str = Field(description="Unit price for completion tokens")
    completion_price_unit: str = Field(description="Price unit for completion tokens")
    completion_price: str = Field(description="Total price for completion tokens")
    
    total_tokens: int = Field(description="Total number of tokens used")
    total_price: str = Field(description="Total price for the request")
    currency: str = Field(description="Currency for pricing")
    latency: float = Field(description="Request latency in seconds")
    
    @field_validator('prompt_tokens', 'completion_tokens', 'total_tokens')
    @classmethod
    def validate_token_counts(cls, v):
        """Ensure token counts are non-negative."""
        if v < 0:
            raise ValueError("Token counts must be non-negative")
        return v
    
    @field_validator('latency')
    @classmethod
    def validate_latency(cls, v):
        """Ensure latency is non-negative."""
        if v < 0:
            raise ValueError("Latency must be non-negative")
        return v


class RetrieverResource(BaseModel):
    """Information about retrieved resources (for RAG applications)."""
    
    position: int = Field(description="Position in the retrieval results")
    dataset_id: str = Field(description="ID of the dataset")
    dataset_name: str = Field(description="Name of the dataset")
    document_id: str = Field(description="ID of the document")
    document_name: str = Field(description="Name of the document")
    segment_id: str = Field(description="ID of the segment")
    score: float = Field(description="Relevance score")
    content: str = Field(description="Retrieved content")
    
    @field_validator('position')
    @classmethod
    def validate_position(cls, v):
        """Ensure position is positive."""
        if v <= 0:
            raise ValueError("Position must be positive")
        return v
    
    @field_validator('score')
    @classmethod
    def validate_score(cls, v):
        """Ensure score is between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Score must be between 0 and 1")
        return v


class FileInfo(BaseModel):
    """Information about uploaded files."""
    
    id: str = Field(description="File ID")
    name: str = Field(description="File name")
    size: int = Field(description="File size in bytes")
    extension: str = Field(description="File extension")
    mime_type: str = Field(description="MIME type")
    created_by: str = Field(description="User who created the file")
    created_at: int = Field(description="Creation timestamp")
    
    @field_validator('size')
    @classmethod
    def validate_size(cls, v):
        """Ensure file size is non-negative."""
        if v < 0:
            raise ValueError("File size must be non-negative")
        return v


class ErrorResponse(BaseModel):
    """Standard error response format."""
    
    code: str = Field(description="Error code")
    message: str = Field(description="Error message")
    status: int = Field(description="HTTP status code")
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional error details"
    )
    
    @field_validator('status')
    @classmethod
    def validate_status(cls, v):
        """Ensure status is a valid HTTP status code."""
        if not 100 <= v <= 599:
            raise ValueError("Status must be a valid HTTP status code")
        return v


class PaginationRequest(BaseModel):
    """Base pagination parameters for list requests."""
    
    limit: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Number of items to return (1-100)"
    )
    offset: int = Field(
        default=0,
        ge=0,
        description="Number of items to skip"
    )


class PaginationResponse(BaseModel):
    """Base pagination information for list responses."""
    
    total: int = Field(description="Total number of items")
    limit: int = Field(description="Number of items per page")
    offset: int = Field(description="Number of items skipped")
    has_more: bool = Field(description="Whether there are more items")
    
    @field_validator('total', 'limit', 'offset')
    @classmethod
    def validate_pagination_values(cls, v):
        """Ensure pagination values are non-negative."""
        if v < 0:
            raise ValueError("Pagination values must be non-negative")
        return v


class StreamEvent(BaseModel):
    """Base class for streaming events."""
    
    event: str = Field(description="Event type")
    data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Event data"
    )


class ConversationVariable(BaseModel):
    """Conversation variable for dynamic session management."""
    
    key: str = Field(description="Variable key")
    value: Union[str, int, float, bool] = Field(description="Variable value")
    
    @field_validator('key')
    @classmethod
    def validate_key(cls, v):
        """Ensure key is not empty."""
        if not v.strip():
            raise ValueError("Variable key cannot be empty")
        return v.strip()


class InputVariable(BaseModel):
    """Input variable for API requests."""
    
    key: str = Field(description="Variable key")
    value: Union[str, int, float, bool, List, Dict] = Field(description="Variable value")
    
    @field_validator('key')
    @classmethod
    def validate_key(cls, v):
        """Ensure key is not empty."""
        if not v.strip():
            raise ValueError("Input key cannot be empty")
        return v.strip()


# Utility functions for model conversion
def dict_to_input_variables(data: Dict[str, Any]) -> List[InputVariable]:
    """Convert a dictionary to a list of InputVariable objects."""
    return [InputVariable(key=k, value=v) for k, v in data.items()]


def input_variables_to_dict(variables: List[InputVariable]) -> Dict[str, Any]:
    """Convert a list of InputVariable objects to a dictionary."""
    return {var.key: var.value for var in variables}
