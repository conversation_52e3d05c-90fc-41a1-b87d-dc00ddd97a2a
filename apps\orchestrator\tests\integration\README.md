# Presentation Layer

This directory contains the presentation layer components for the orchestrator lambda function.

## Components

- `lambda_handler.py` - Main AWS Lambda handler function
- `lambda_utils.py` - Utility functions for Lambda request/response handling

## Dify Agent Testing

The Dify agent invoke functionality has been converted to proper integration tests.
See `tests/integration/test_dify_agent_invoke.py` for comprehensive test cases.

## Running Tests

To test the Dify agent functionality, use the integration test runner:

```bash
cd apps/orchestrator
python run_integration_tests.py --dify
```

## Prerequisites

1. **Environment Configuration**: Ensure `.env.local` file exists in the orchestrator directory with:
   ```
   DIFY_API_KEY=your-dify-api-key
   DIFY_BASE_URL=https://api.dify.ai/v1
   DIFY_TIMEOUT_SECONDS=30
   ```

2. **Python Dependencies**: The Dify SDK and its dependencies should be available in the orchestrator directory.

## Test Coverage

The integration tests cover:
- Streaming mode functionality (primary mode for Agent Chat Apps)
- Blocking mode error handling (expected to fail)
- Conversation continuation with conversation IDs
- Authentication error handling
- Missing input parameter handling
- Client configuration testing

## Key Findings

1. **Response Modes**:
   - ✅ **Streaming mode**: Fully supported and working
   - ❌ **Blocking mode**: Not supported by Agent Chat Apps (will return 400 error)

2. **Authentication**: Uses Bearer token authentication with the API key from environment variables.

3. **Input Parameters**: The agent expects specific input parameters like `company_name`, `system_instructions`, and `product_catalog`.

## Integration

The test cases serve as examples for integrating Dify agent functionality into the main orchestrator lambda handler.
