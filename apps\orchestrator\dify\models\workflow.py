"""
Workflow Models for Dify SDK

Pydantic models for workflow execution API requests and responses.
"""

from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, field_validator

from .common import (
    BaseRequest,
    BaseResponse,
    ResponseMode,
    Usage,
    StreamEvent,
)


class WorkflowRequest(BaseRequest):
    """Request model for workflow execution."""
    
    inputs: Dict[str, Any] = Field(
        description="Input variables for the workflow"
    )
    response_mode: ResponseMode = Field(
        default=ResponseMode.BLOCKING,
        description="Response mode: blocking or streaming"
    )
    user: str = Field(
        description="User identifier"
    )
    files: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Files to include with the workflow execution"
    )
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()
    
    @field_validator('inputs')
    @classmethod
    def validate_inputs(cls, v):
        """Ensure inputs is a valid dictionary."""
        if not isinstance(v, dict):
            raise ValueError("Inputs must be a dictionary")
        return v


class WorkflowNodeExecution(BaseModel):
    """Information about a workflow node execution."""
    
    id: str = Field(description="Node execution ID")
    node_id: str = Field(description="Node ID")
    node_type: str = Field(description="Type of the node")
    title: str = Field(description="Node title")
    index: int = Field(description="Execution index")
    predecessor_node_id: Optional[str] = Field(
        default=None,
        description="ID of the predecessor node"
    )
    inputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Node inputs"
    )
    process_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Node processing data"
    )
    outputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Node outputs"
    )
    status: str = Field(description="Execution status")
    error: Optional[str] = Field(
        default=None,
        description="Error message if execution failed"
    )
    elapsed_time: Optional[float] = Field(
        default=None,
        description="Execution time in seconds"
    )
    execution_metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional execution metadata"
    )
    created_at: int = Field(description="Creation timestamp")
    finished_at: Optional[int] = Field(
        default=None,
        description="Completion timestamp"
    )
    
    class Status:
        RUNNING = "running"
        SUCCEEDED = "succeeded"
        FAILED = "failed"
        STOPPED = "stopped"


class WorkflowMetadata(BaseModel):
    """Metadata included in workflow responses."""
    
    usage: Optional[Usage] = Field(
        default=None,
        description="Token usage information"
    )
    total_steps: Optional[int] = Field(
        default=None,
        description="Total number of workflow steps"
    )
    total_tokens: Optional[int] = Field(
        default=None,
        description="Total tokens used in workflow"
    )


class WorkflowResponse(BaseResponse):
    """Response model for workflow execution."""
    
    workflow_run_id: str = Field(description="Workflow run identifier")
    task_id: str = Field(description="Task identifier")
    data: Dict[str, Any] = Field(description="Workflow output data")
    outputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Workflow outputs"
    )
    status: str = Field(description="Workflow execution status")
    error: Optional[str] = Field(
        default=None,
        description="Error message if workflow failed"
    )
    elapsed_time: float = Field(description="Total execution time in seconds")
    total_steps: int = Field(description="Total number of steps executed")
    total_tokens: int = Field(description="Total tokens consumed")
    created_at: int = Field(description="Creation timestamp")
    finished_at: int = Field(description="Completion timestamp")
    
    class Status:
        RUNNING = "running"
        SUCCEEDED = "succeeded"
        FAILED = "failed"
        STOPPED = "stopped"


class WorkflowStreamEvent(StreamEvent):
    """Streaming event for workflow execution."""
    
    # Specific event types for workflow streaming
    workflow_run_id: Optional[str] = Field(default=None, description="Workflow run identifier")
    task_id: Optional[str] = Field(default=None, description="Task identifier")
    node_execution: Optional[WorkflowNodeExecution] = Field(
        default=None,
        description="Node execution information"
    )
    
    # For different event types
    class EventTypes:
        WORKFLOW_STARTED = "workflow_started"
        WORKFLOW_FINISHED = "workflow_finished"
        NODE_STARTED = "node_started"
        NODE_FINISHED = "node_finished"
        ERROR = "error"
        PING = "ping"


class WorkflowStopRequest(BaseRequest):
    """Request model for stopping workflow execution."""
    
    task_id: str = Field(description="Task ID of the workflow to stop")
    user: str = Field(description="User identifier")
    
    @field_validator('task_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class WorkflowStopResponse(BaseResponse):
    """Response model for stopping workflow execution."""
    
    result: str = Field(description="Result of the stop operation")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"
        NOT_FOUND = "not_found"


class WorkflowRunListRequest(BaseRequest):
    """Request model for listing workflow runs."""
    
    user: str = Field(description="User identifier")
    limit: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Number of runs to return (1-100)"
    )
    offset: int = Field(
        default=0,
        ge=0,
        description="Number of runs to skip"
    )
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()


class WorkflowRunInfo(BaseModel):
    """Information about a workflow run."""
    
    id: str = Field(description="Workflow run ID")
    workflow_id: str = Field(description="Workflow ID")
    status: str = Field(description="Run status")
    inputs: Dict[str, Any] = Field(description="Run inputs")
    outputs: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Run outputs"
    )
    error: Optional[str] = Field(
        default=None,
        description="Error message if run failed"
    )
    elapsed_time: Optional[float] = Field(
        default=None,
        description="Execution time in seconds"
    )
    total_steps: Optional[int] = Field(
        default=None,
        description="Total number of steps"
    )
    total_tokens: Optional[int] = Field(
        default=None,
        description="Total tokens used"
    )
    created_by: str = Field(description="User who created the run")
    created_at: int = Field(description="Creation timestamp")
    finished_at: Optional[int] = Field(
        default=None,
        description="Completion timestamp"
    )


class WorkflowRunListResponse(BaseResponse):
    """Response model for workflow run list."""
    
    data: List[WorkflowRunInfo] = Field(
        default_factory=list,
        description="List of workflow runs"
    )
    total: int = Field(description="Total number of runs")
    page: int = Field(description="Current page number")
    limit: int = Field(description="Number of items per page")
    has_more: bool = Field(description="Whether there are more items")


# Utility functions for workflow models
def create_workflow_request(
    inputs: Dict[str, Any],
    user: str,
    response_mode: ResponseMode = ResponseMode.BLOCKING,
    files: Optional[List[Dict[str, Any]]] = None,
    **kwargs
) -> WorkflowRequest:
    """
    Create a WorkflowRequest with common parameters.
    
    Args:
        inputs: Input variables for the workflow
        user: User identifier
        response_mode: Response mode (blocking or streaming)
        files: Optional files to include
        **kwargs: Additional parameters
        
    Returns:
        WorkflowRequest instance
    """
    return WorkflowRequest(
        inputs=inputs,
        user=user,
        response_mode=response_mode,
        files=files,
        **kwargs
    )


def parse_workflow_stream_event(event_data: str) -> Optional[WorkflowStreamEvent]:
    """
    Parse a streaming event from SSE data.
    
    Args:
        event_data: Raw event data string
        
    Returns:
        Parsed WorkflowStreamEvent or None if parsing fails
    """
    try:
        import json
        data = json.loads(event_data)
        return WorkflowStreamEvent(**data)
    except Exception:
        return None
