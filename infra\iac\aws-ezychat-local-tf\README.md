# EzyChat LocalStack Development Environment

This directory provides a complete LocalStack testing environment for the EzyChat document ingestion Lambda function. It uses Docker containers and ECR for Lambda deployment, maintaining consistency with the production setup.

## 🏗️ Architecture

The LocalStack environment replicates the production infrastructure:

- **Lambda Function**: Container-based deployment using LocalStack ECR
- **S3 Storage**: Document processing buckets with event notifications
- **SSM Parameter Store**: Secrets and configuration management
- **API Gateway**: HTTP endpoints for Lambda invocation
- **CloudWatch**: Logging and monitoring
- **ECR**: Container registry for Lambda images

## 📋 Prerequisites

### Required Software
- **Docker & Docker Compose**: For LocalStack services
- **LocalStack Pro License**: Required for ECR support
- **AWS CLI**: For LocalStack operations
- **tflocal**: Terraform wrapper for LocalStack
- **jq**: JSON processing (for scripts)

### Installation Commands
```bash
# Install tflocal
pip install terraform-local

# Install AWS CLI (if not already installed)
pip install awscli

# Install jq (macOS)
brew install jq

# Install jq (Ubuntu/Debian)
sudo apt-get install jq
```

### LocalStack Pro Setup
1. Sign up for LocalStack Pro at https://app.localstack.cloud/
2. Get your API key from the dashboard
3. Set the environment variable: `export LOCALSTACK_API_KEY=your-api-key`

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.local.example .env.local

# Edit .env.local with your credentials
# - LocalStack Pro API key
# - OpenAI API key (for testing)
# - Supabase credentials (for testing)
```

### 2. Complete Development Setup
```bash
# One-command setup (starts LocalStack, deploys infrastructure, sets up test data)
make dev-setup
```

### 3. Test the Lambda Function
```bash
# Test S3 event processing
make test-s3-event

# View Lambda logs
make lambda-logs
```

## 📖 Detailed Usage

### LocalStack Management
```bash
# Start LocalStack services
make localstack-start

# Check LocalStack health
make localstack-status

# View LocalStack logs
make localstack-logs

# Stop LocalStack
make localstack-stop
```

### Infrastructure Deployment
```bash
# Initialize Terraform
make tflocal-init

# Plan infrastructure changes
make tflocal-plan

# Deploy infrastructure
make tflocal-apply

# Destroy infrastructure
make tflocal-destroy
```

### Lambda Development Workflow
```bash
# Build Lambda container image
make lambda-build

# Push image to LocalStack ECR
make lambda-push

# Complete build and deploy
make lambda-deploy

# Update function after code changes
make lambda-update

# View function logs
make lambda-logs
```

### Testing and Validation
```bash
# Setup test environment (SSM parameters, test data)
make test-setup

# Test S3 event processing
make test-s3-event

# Run full test suite
make test-full
```

## 🔧 Configuration

### Environment Variables (.env.local)
```bash
# LocalStack Pro
LOCALSTACK_API_KEY=your-localstack-pro-api-key

# External Services (for testing)
OPENAI_API_KEY=sk-your-test-openai-api-key
SUPABASE_URL=https://your-test-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-test-service-key
SUPABASE_ANON_KEY=your-test-anon-key

# Processing Configuration
MAX_FILE_SIZE_BYTES=52428800
MAX_ROWS=100000
BATCH_SIZE=100
```

### SSM Parameters
The setup script creates these parameters in LocalStack:
- `/ezychat/document-ingestion/openai/api-key`
- `/ezychat/document-ingestion/supabase/url`
- `/ezychat/document-ingestion/supabase/service-role-key`
- `/ezychat/document-ingestion/processing/*`

## 🧪 Testing Scenarios

### 1. S3 Event Processing
```bash
# Upload test CSV to trigger Lambda
aws --endpoint-url=http://localhost:4566 s3 cp test-data/sample-products.csv \
  s3://ezychat-documents-local/user123/incoming/products.csv

# Check processing results
make lambda-logs
```

### 2. Direct Lambda Invocation
```bash
# Invoke Lambda with test S3 event
make test-s3-event

# Check response
cat response.json | jq .
```

### 3. API Gateway Testing
```bash
# Test HTTP endpoints (when implemented)
make test-api
```

## 🛠️ Development Workflow

### Code Changes
1. Make changes to Lambda code in `apps/document-ingestion/`
2. Update the function: `make lambda-update`
3. Test changes: `make test-s3-event`
4. View logs: `make lambda-logs`

### Infrastructure Changes
1. Modify Terraform files
2. Plan changes: `make tflocal-plan`
3. Apply changes: `make tflocal-apply`

### Complete Reset
```bash
# Reset entire environment
make reset

# Start fresh
make dev-setup
```

## 📁 Directory Structure
```
infra/iac/aws-ezychat-local-tf/
├── Makefile                     # Main automation
├── docker-compose.yml           # LocalStack services
├── main.tf                      # Core infrastructure import
├── provider.tf                  # LocalStack provider
├── localstack_overrides.tf      # LocalStack-specific resources
├── .env.local.example          # Environment template
├── scripts/
│   └── setup-parameters.sh     # SSM parameter setup
├── test-data/
│   ├── sample-products.csv     # Test CSV file
│   └── s3-event.json          # Test S3 event
└── README.md                   # This file
```

## 🔍 Troubleshooting

### Common Issues

#### LocalStack Not Starting
```bash
# Check Docker is running
docker ps

# Check LocalStack API key
echo $LOCALSTACK_API_KEY

# View LocalStack logs
make localstack-logs
```

#### Lambda Function Not Deploying
```bash
# Check ECR repository exists
aws --endpoint-url=http://localhost:4566 ecr describe-repositories

# Rebuild and redeploy
make lambda-deploy
```

#### External Services Not Working
```bash
# Check SSM parameters
aws --endpoint-url=http://localhost:4566 ssm get-parameters-by-path \
  --path "/ezychat/document-ingestion" --recursive

# Verify credentials in .env.local
```

### Debug Mode
```bash
# Enable verbose logging
export DEBUG=1

# View detailed LocalStack logs
make localstack-logs
```

## 🎯 Next Steps

1. **Complete Setup**: Run `make dev-setup` to get started
2. **Test Processing**: Upload CSV files and verify processing
3. **Develop Features**: Make code changes and test with `make lambda-update`
4. **Add Tests**: Extend test scenarios for your use cases

## 📚 Additional Resources

- [LocalStack Documentation](https://docs.localstack.cloud/)
- [LocalStack Pro Features](https://docs.localstack.cloud/localstack/pro/)
- [AWS Lambda Container Images](https://docs.aws.amazon.com/lambda/latest/dg/images-create.html)
- [Terraform LocalStack Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/guides/custom-service-endpoints)
