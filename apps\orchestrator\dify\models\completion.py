"""
Completion Models for Dify SDK

Pydantic models for text completion API requests and responses.
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator

from .common import (
    BaseRequest,
    BaseResponse,
    ResponseMode,
    Usage,
    StreamEvent,
)


class CompletionRequest(BaseRequest):
    """Request model for text completion."""
    
    inputs: Dict[str, Any] = Field(
        description="Input variables for the completion application"
    )
    response_mode: ResponseMode = Field(
        default=ResponseMode.BLOCKING,
        description="Response mode: blocking or streaming"
    )
    user: str = Field(
        description="User identifier"
    )
    files: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Files to include with the completion request"
    )
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()
    
    @field_validator('inputs')
    @classmethod
    def validate_inputs(cls, v):
        """Ensure inputs is a valid dictionary."""
        if not isinstance(v, dict):
            raise ValueError("Inputs must be a dictionary")
        return v


class CompletionMetadata(BaseModel):
    """Metadata included in completion responses."""
    
    usage: Optional[Usage] = Field(
        default=None,
        description="Token usage information"
    )


class CompletionResponse(BaseResponse):
    """Response model for text completion."""
    
    event: str = Field(description="Event type")
    task_id: str = Field(description="Task identifier")
    id: str = Field(description="Completion identifier")
    mode: str = Field(description="Application mode")
    answer: str = Field(description="Generated completion text")
    metadata: Optional[CompletionMetadata] = Field(
        default=None,
        description="Response metadata"
    )
    created_at: int = Field(description="Creation timestamp")
    
    @field_validator('answer')
    @classmethod
    def validate_answer(cls, v):
        """Ensure answer is a string."""
        return str(v) if v is not None else ""


class CompletionStreamEvent(StreamEvent):
    """Streaming event for completion responses."""
    
    # Specific event types for completion streaming
    task_id: Optional[str] = Field(default=None, description="Task identifier")
    id: Optional[str] = Field(default=None, description="Completion identifier")
    answer: Optional[str] = Field(default=None, description="Partial or complete answer")
    created_at: Optional[int] = Field(default=None, description="Creation timestamp")
    
    # For different event types
    class EventTypes:
        TEXT_CHUNK = "text_chunk"
        TEXT_REPLACE = "text_replace"
        TEXT_END = "text_end"
        ERROR = "error"
        PING = "ping"


class CompletionStopRequest(BaseRequest):
    """Request model for stopping completion generation."""
    
    task_id: str = Field(description="Task ID of the completion to stop")
    user: str = Field(description="User identifier")
    
    @field_validator('task_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class CompletionStopResponse(BaseResponse):
    """Response model for stopping completion generation."""
    
    result: str = Field(description="Result of the stop operation")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"
        NOT_FOUND = "not_found"


class CompletionFeedbackRequest(BaseRequest):
    """Request model for providing feedback on completions."""
    
    message_id: str = Field(description="Completion ID to provide feedback for")
    rating: str = Field(description="Feedback rating")
    content: Optional[str] = Field(
        default=None,
        description="Optional feedback content/comment"
    )
    user: str = Field(description="User identifier")
    
    @field_validator('message_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()
    
    @field_validator('rating')
    @classmethod
    def validate_rating(cls, v):
        """Validate rating value."""
        valid_ratings = {'like', 'dislike'}
        if v not in valid_ratings:
            raise ValueError(f"Rating must be one of: {', '.join(valid_ratings)}")
        return v
    
    class Ratings:
        LIKE = "like"
        DISLIKE = "dislike"


class CompletionFeedbackResponse(BaseResponse):
    """Response model for completion feedback."""
    
    result: str = Field(description="Result status")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"


# Utility functions for completion models
def create_completion_request(
    inputs: Dict[str, Any],
    user: str,
    response_mode: ResponseMode = ResponseMode.BLOCKING,
    files: Optional[List[Dict[str, Any]]] = None,
    **kwargs
) -> CompletionRequest:
    """
    Create a CompletionRequest with common parameters.
    
    Args:
        inputs: Input variables for the completion
        user: User identifier
        response_mode: Response mode (blocking or streaming)
        files: Optional files to include
        **kwargs: Additional parameters
        
    Returns:
        CompletionRequest instance
    """
    return CompletionRequest(
        inputs=inputs,
        user=user,
        response_mode=response_mode,
        files=files,
        **kwargs
    )


def parse_completion_stream_event(event_data: str) -> Optional[CompletionStreamEvent]:
    """
    Parse a streaming event from SSE data.
    
    Args:
        event_data: Raw event data string
        
    Returns:
        Parsed CompletionStreamEvent or None if parsing fails
    """
    try:
        import json
        data = json.loads(event_data)
        return CompletionStreamEvent(**data)
    except Exception:
        return None


# Template for common completion inputs
class CommonCompletionInputs:
    """Common input templates for completion requests."""
    
    @staticmethod
    def text_generation(text: str, **kwargs) -> Dict[str, Any]:
        """Create inputs for text generation."""
        inputs = {"text": text}
        inputs.update(kwargs)
        return inputs
    
    @staticmethod
    def summarization(content: str, max_length: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """Create inputs for text summarization."""
        inputs = {"content": content}
        if max_length:
            inputs["max_length"] = max_length
        inputs.update(kwargs)
        return inputs
    
    @staticmethod
    def translation(text: str, target_language: str, source_language: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Create inputs for text translation."""
        inputs = {
            "text": text,
            "target_language": target_language
        }
        if source_language:
            inputs["source_language"] = source_language
        inputs.update(kwargs)
        return inputs
    
    @staticmethod
    def question_answering(question: str, context: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Create inputs for question answering."""
        inputs = {"question": question}
        if context:
            inputs["context"] = context
        inputs.update(kwargs)
        return inputs
