"""
Rate Limiting for Dify SDK

Implements client-side rate limiting to prevent exceeding API limits
and ensure smooth operation under high load.
"""

import time
import threading
from collections import deque
from typing import Optional
from dataclasses import dataclass

from ..config import RateLimitConfig


class RateLimiter:
    """
    Token bucket rate limiter for API requests.
    
    This implementation uses a token bucket algorithm to enforce rate limits
    while allowing for burst traffic up to a configured limit.
    """
    
    def __init__(self, config: RateLimitConfig):
        """
        Initialize the rate limiter.
        
        Args:
            config: RateLimitConfig instance with rate limiting parameters
        """
        self.config = config
        self.requests_per_second = config.requests_per_minute / 60.0
        self.burst_limit = config.burst_limit
        
        # Token bucket state
        self.tokens = float(self.burst_limit)
        self.last_update = time.time()
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Request history for sliding window (backup method)
        self._request_times = deque()
    
    def acquire(self, tokens: int = 1) -> None:
        """
        Acquire tokens from the rate limiter.
        
        This method will block if necessary to respect the rate limit.
        
        Args:
            tokens: Number of tokens to acquire (default: 1)
        """
        with self._lock:
            self._refill_tokens()
            
            if self.tokens >= tokens:
                # Tokens available, consume them
                self.tokens -= tokens
                self._record_request()
            else:
                # Not enough tokens, calculate wait time
                wait_time = self._calculate_wait_time(tokens)
                if wait_time > 0:
                    # Release lock while waiting
                    pass
                
                # Wait outside the lock to avoid blocking other threads
                if wait_time > 0:
                    time.sleep(wait_time)
                
                # Try again after waiting
                self._refill_tokens()
                self.tokens = max(0, self.tokens - tokens)
                self._record_request()
    
    def try_acquire(self, tokens: int = 1) -> bool:
        """
        Try to acquire tokens without blocking.
        
        Args:
            tokens: Number of tokens to acquire (default: 1)
            
        Returns:
            True if tokens were acquired, False otherwise
        """
        with self._lock:
            self._refill_tokens()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                self._record_request()
                return True
            return False
    
    def _refill_tokens(self) -> None:
        """Refill the token bucket based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_update
        
        # Add tokens based on elapsed time
        tokens_to_add = elapsed * self.requests_per_second
        self.tokens = min(self.burst_limit, self.tokens + tokens_to_add)
        self.last_update = now
    
    def _calculate_wait_time(self, tokens_needed: int) -> float:
        """
        Calculate how long to wait for the required tokens.
        
        Args:
            tokens_needed: Number of tokens needed
            
        Returns:
            Wait time in seconds
        """
        tokens_deficit = tokens_needed - self.tokens
        if tokens_deficit <= 0:
            return 0.0
        
        # Calculate time needed to generate the required tokens
        return tokens_deficit / self.requests_per_second
    
    def _record_request(self) -> None:
        """Record a request timestamp for sliding window tracking."""
        now = time.time()
        self._request_times.append(now)
        
        # Clean old requests (older than 1 minute)
        cutoff = now - 60.0
        while self._request_times and self._request_times[0] < cutoff:
            self._request_times.popleft()
    
    def get_current_rate(self) -> float:
        """
        Get the current request rate (requests per minute).
        
        Returns:
            Current request rate based on recent history
        """
        with self._lock:
            now = time.time()
            cutoff = now - 60.0
            
            # Count requests in the last minute
            recent_requests = sum(1 for t in self._request_times if t >= cutoff)
            return float(recent_requests)
    
    def get_available_tokens(self) -> float:
        """
        Get the number of currently available tokens.
        
        Returns:
            Number of available tokens
        """
        with self._lock:
            self._refill_tokens()
            return self.tokens
    
    def reset(self) -> None:
        """Reset the rate limiter state."""
        with self._lock:
            self.tokens = float(self.burst_limit)
            self.last_update = time.time()
            self._request_times.clear()


class SlidingWindowRateLimiter:
    """
    Alternative sliding window rate limiter implementation.
    
    This implementation tracks request timestamps in a sliding window
    and is more precise but potentially more memory-intensive.
    """
    
    def __init__(self, config: RateLimitConfig):
        """
        Initialize the sliding window rate limiter.
        
        Args:
            config: RateLimitConfig instance with rate limiting parameters
        """
        self.config = config
        self.window_size = 60.0  # 1 minute window
        self.max_requests = config.requests_per_minute
        
        # Request timestamps
        self._request_times = deque()
        self._lock = threading.Lock()
    
    def acquire(self) -> None:
        """Acquire permission to make a request, blocking if necessary."""
        with self._lock:
            self._cleanup_old_requests()
            
            if len(self._request_times) < self.max_requests:
                # Under the limit, allow the request
                self._request_times.append(time.time())
            else:
                # At the limit, calculate wait time
                oldest_request = self._request_times[0]
                wait_time = self.window_size - (time.time() - oldest_request)
                
                if wait_time > 0:
                    # Wait outside the lock
                    pass
                
                # Wait and try again
                if wait_time > 0:
                    time.sleep(wait_time)
                
                self._cleanup_old_requests()
                self._request_times.append(time.time())
    
    def try_acquire(self) -> bool:
        """Try to acquire permission without blocking."""
        with self._lock:
            self._cleanup_old_requests()
            
            if len(self._request_times) < self.max_requests:
                self._request_times.append(time.time())
                return True
            return False
    
    def _cleanup_old_requests(self) -> None:
        """Remove request timestamps outside the sliding window."""
        now = time.time()
        cutoff = now - self.window_size
        
        while self._request_times and self._request_times[0] < cutoff:
            self._request_times.popleft()
    
    def get_current_count(self) -> int:
        """Get the current number of requests in the window."""
        with self._lock:
            self._cleanup_old_requests()
            return len(self._request_times)


# Context manager for rate limiting
class RateLimitedContext:
    """Context manager for rate-limited operations."""
    
    def __init__(self, rate_limiter: RateLimiter, tokens: int = 1):
        """
        Initialize the context manager.
        
        Args:
            rate_limiter: RateLimiter instance
            tokens: Number of tokens to acquire
        """
        self.rate_limiter = rate_limiter
        self.tokens = tokens
    
    def __enter__(self):
        """Acquire tokens when entering the context."""
        self.rate_limiter.acquire(self.tokens)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Nothing to do when exiting the context."""
        pass
