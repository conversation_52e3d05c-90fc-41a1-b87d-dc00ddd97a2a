# Orchestrator Lambda Function

## Overview

The Orchestrator Lambda function integrates with Dify Cloud Agent API to provide AI-powered sales assistant capabilities for WhatsApp messages. This Lambda function processes incoming WhatsApp messages through the Dify Agent API and returns intelligent responses.

## Features

- **Dify Agent Integration**: Uses local Dify wrapper package for API communication
- **Clean Architecture**: Implements Clean Architecture with dependency injection
- **Streaming Support**: Handles Dify streaming responses internally, returns blocking responses
- **Hardcoded Configuration**: Uses hardcoded tenant configurations (extensible for DynamoDB)
- **Error Handling**: Comprehensive error handling and logging
- **Mock Integration**: Includes mock WhatsApp Manager for testing
- **Self-Contained**: Includes local copy of Dify library for easy deployment

## Architecture

```
├── src/
│   ├── application/
│   │   ├── interfaces/          # Application interfaces
│   │   └── use_cases/          # Business use cases
│   ├── domain/
│   │   ├── entities/           # Domain entities
│   │   └── services/           # Domain services
│   ├── infrastructure/
│   │   ├── services/           # Infrastructure implementations
│   │   ├── configuration.py    # Configuration management
│   │   └── dependency_injection.py  # DI container
│   └── presentation/
│       └── lambda_handler.py   # Lambda entry point
├── dify/                       # Local Dify API wrapper library
├── tests/                      # Test suite
└── Dockerfile                  # Container configuration
```

## API Contract

### Input (WhatsApp Message)
```json
{
  "phone_number": "+1234567890",
  "message_content": "Hello, I need help with your products",
  "conversation_id": "optional_conversation_id",
  "message_id": "unique_message_id"
}
```

### Output (Agent Response)
```json
{
  "success": true,
  "conversation_id": "dify_conversation_id",
  "response_text": "AI agent response text",
  "tenant_id": "demo_tenant_001",
  "metadata": {
    "processing_time_ms": 1500,
    "dify_response_id": "dify_message_id",
    "company_name": "EzyChat Demo Company"
  }
}
```

## Environment Variables

- `DIFY_API_KEY`: Dify API key for authentication (required)
- `DIFY_BASE_URL`: Dify API base URL (default: https://api.dify.ai/v1)
- `LOG_LEVEL`: Logging level (default: INFO)
- `PYTHON_ENV`: Environment name (default: production)
- `DIFY_TIMEOUT_SECONDS`: Dify API timeout (default: 30)

## Development

### Prerequisites

- Python 3.12
- Docker (for containerized deployment)
- AWS CLI (for deployment)

### Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run tests:
```bash
make test
```

Or manually:
```bash
# Windows PowerShell
$env:PYTHONPATH="src;."
pytest

# Linux/Mac
PYTHONPATH=src:. pytest
```

### Testing

The project includes comprehensive test coverage:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test Dify API integration
- **Mock Tests**: Test with mock WhatsApp Manager

Run tests with coverage:
```bash
make test-coverage
```

Run only unit tests:
```bash
make test-unit
```

**Note**: Some tests may be skipped if the full infrastructure dependencies cannot be imported. This is expected during development and the core functionality is still tested through the working tests.

## Deployment

### Docker Build

**Option 1: Use the Makefile**
```bash
make build
```

**Option 3: Manual build**
```bash
# Build from orchestrator directory
docker build -t lambda-python-orchestrator .
```

### AWS Lambda Deployment

The function is designed for container-based Lambda deployment:

1. Build and push to ECR:
```bash
# Build image
docker build -t orchestrator-lambda .

# Or use Makefile
make build-prod

# Push to ECR
make push
```

2. Deploy Lambda function with:
   - Memory: 1024MB
   - Timeout: 60 seconds
   - Environment variables configured
   - API Gateway integration

## Configuration

### Tenant Configuration

Currently uses hardcoded tenant configurations in `ConfigurationService`:

```python
TENANT_CONFIGS = {
    "default": {
        "tenant_id": "demo_tenant_001",
        "company_name": "EzyChat Demo Company",
        "system_instructions": "You are a helpful sales assistant...",
        "product_catalog": "EzyChat Pro: $99/month..."
    }
}
```

### Future Enhancements

- DynamoDB tenant configuration lookup
- Real WhatsApp Manager integration
- End-to-end streaming implementation
- Advanced tenant management features

## Monitoring

The function includes comprehensive logging for:
- Request processing times
- Dify API interactions
- Error tracking
- Performance metrics

## Security

- API key stored in environment variables
- Input validation on all requests
- Error messages sanitized for security
- CORS headers configured for web access
