import { injectable } from 'tsyringe';
import {
  AuthToken,
  AuthTokenError,
  AuthTokenNotFoundError,
  AuthTokenExpiredError,
  AuthTokenUsedError
} from '../../domain/value-objects/AuthToken';
import { IAuthTokenRepository } from '../../domain/repositories/IAuthTokenRepository';

/**
 * Mock implementation of AuthTokenRepository for testing
 * Stores auth tokens in memory instead of DynamoDB
 */
@injectable()
export class MockAuthTokenRepository implements IAuthTokenRepository {
  private tokens = new Map<string, AuthToken>();
  private tokensByString = new Map<string, AuthToken>();

  async create(token: AuthToken): Promise<void> {
    if (this.tokens.has(token.tokenId)) {
      throw new AuthTokenError(`Auth token with ID ${token.tokenId} already exists`);
    }
    
    this.tokens.set(token.tokenId, token);
    this.tokensByString.set(token.token, token);
  }

  async findByTokenId(tokenId: string): Promise<AuthToken | null> {
    return this.tokens.get(tokenId) || null;
  }

  async findByToken(tokenString: string): Promise<AuthToken | null> {
    return this.tokensByString.get(tokenString) || null;
  }

  async findByJti(jti: string): Promise<AuthToken | null> {
    // For this mock, we'll treat JTI as the tokenId
    return this.tokens.get(jti) || null;
  }

  async findByUserId(userId: string, includeExpired?: boolean): Promise<AuthToken[]> {
    const userTokens = Array.from(this.tokens.values()).filter(token => token.userId === userId);
    if (includeExpired === false) {
      return userTokens.filter(token => !token.isExpired());
    }
    return userTokens;
  }

  // Helper methods for internal use

  private async findExpiredTokens(): Promise<AuthToken[]> {
    return Array.from(this.tokens.values()).filter(token => token.isExpired());
  }

  private async findUsedTokens(): Promise<AuthToken[]> {
    return Array.from(this.tokens.values()).filter(token => token.isUsed);
  }



  async update(token: AuthToken): Promise<void> {
    if (!this.tokens.has(token.tokenId)) {
      throw new AuthTokenNotFoundError(`Auth token with ID ${token.tokenId} not found`);
    }
    
    // Remove old token string mapping
    const oldToken = this.tokens.get(token.tokenId);
    if (oldToken) {
      this.tokensByString.delete(oldToken.token);
    }
    
    this.tokens.set(token.tokenId, token);
    this.tokensByString.set(token.token, token);
  }

  async markAsUsed(tokenId: string): Promise<AuthToken> {
    const token = this.tokens.get(tokenId);
    if (!token) {
      throw new AuthTokenNotFoundError(`Auth token with ID ${tokenId} not found`);
    }

    if (token.isExpired()) {
      throw new AuthTokenExpiredError('Cannot mark expired token as used');
    }

    if (token.isUsed) {
      throw new AuthTokenUsedError('Token is already marked as used');
    }

    const usedToken = token.markAsUsed();
    await this.update(usedToken);
    return usedToken;
  }

  async delete(tokenId: string): Promise<boolean> {
    const token = this.tokens.get(tokenId);
    if (token) {
      this.tokens.delete(tokenId);
      this.tokensByString.delete(token.token);
      return true;
    }
    return false;
  }

  async deleteByUserId(userId: string): Promise<number> {
    const userTokens = await this.findByUserId(userId);
    for (const token of userTokens) {
      await this.delete(token.tokenId);
    }
    return userTokens.length;
  }

  async deleteExpiredTokens(): Promise<number> {
    const expiredTokens = await this.findExpiredTokens();
    for (const token of expiredTokens) {
      await this.delete(token.tokenId);
    }
    return expiredTokens.length;
  }

  async deleteUsedTokens(): Promise<number> {
    const usedTokens = await this.findUsedTokens();
    for (const token of usedTokens) {
      await this.delete(token.tokenId);
    }
    return usedTokens.length;
  }

  async countActiveTokensByUserId(userId: string): Promise<number> {
    // Active tokens are those that are not expired AND not used
    return Array.from(this.tokens.values()).filter(token =>
      token.userId === userId && !token.isExpired() && !token.isUsed
    ).length;
  }

  async countTotalTokens(includeExpired?: boolean): Promise<number> {
    if (includeExpired === false) {
      return Array.from(this.tokens.values()).filter(token => !token.isExpired()).length;
    }
    return this.tokens.size;
  }

  async findExpiringTokens(withinSeconds: number): Promise<AuthToken[]> {
    const cutoffTime = new Date(Date.now() + (withinSeconds * 1000));
    return Array.from(this.tokens.values()).filter(token =>
      !token.isExpired() && token.expiresAt <= cutoffTime
    );
  }

  async batchCreate(tokens: AuthToken[]): Promise<void> {
    for (const token of tokens) {
      await this.create(token);
    }
  }

  async batchDelete(tokenIds: string[]): Promise<number> {
    let deletedCount = 0;
    for (const tokenId of tokenIds) {
      const deleted = await this.delete(tokenId);
      if (deleted) deletedCount++;
    }
    return deletedCount;
  }

  async exists(tokenId: string): Promise<boolean> {
    return this.tokens.has(tokenId);
  }

  async getStatistics(): Promise<{
    totalTokens: number;
    activeTokens: number;
    expiredTokens: number;
    usedTokens: number;
    tokensCreatedToday: number;
    tokensUsedToday: number;
  }> {
    const allTokens = Array.from(this.tokens.values());
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    return {
      totalTokens: allTokens.length,
      activeTokens: allTokens.filter(token => !token.isExpired() && !token.isUsed).length,
      expiredTokens: allTokens.filter(token => token.isExpired()).length,
      usedTokens: allTokens.filter(token => token.isUsed).length,
      tokensCreatedToday: allTokens.filter(token => token.createdAt >= todayStart).length,
      tokensUsedToday: allTokens.filter(token => token.usedAt && token.usedAt >= todayStart).length,
    };
  }



  async validateToken(tokenId: string): Promise<AuthToken> {
    const token = this.tokens.get(tokenId);
    
    if (!token) {
      throw new AuthTokenNotFoundError(`Auth token with ID ${tokenId} not found`);
    }

    if (token.isExpired()) {
      throw new AuthTokenExpiredError('Auth token has expired');
    }

    if (token.isUsed) {
      throw new AuthTokenUsedError('Auth token has already been used');
    }

    return token;
  }

  async cleanupOldTokens(retentionDays: number): Promise<number> {
    if (retentionDays <= 0) {
      throw new AuthTokenError('Retention days must be positive');
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    const cutoffTimestamp = Math.floor(cutoffDate.getTime() / 1000);

    const tokensToDelete = Array.from(this.tokens.values()).filter(token =>
      Math.floor(token.createdAt.getTime() / 1000) < cutoffTimestamp
    );

    for (const token of tokensToDelete) {
      await this.delete(token.tokenId);
    }

    return tokensToDelete.length;
  }



  // Test helper methods
  clear(): void {
    this.tokens.clear();
    this.tokensByString.clear();
  }

  getAll(): AuthToken[] {
    return Array.from(this.tokens.values());
  }

  getAllByTokenString(): Map<string, AuthToken> {
    return new Map(this.tokensByString);
  }
}
