import { Request, Response, NextFunction } from 'express';
import { ValidationError, validate } from 'express-validation';

/**
 * Validation middleware for request validation using express-validation
 */
export function validateRequest(schema: any) {
  return (req: Request, res: Response, next: NextFunction) => {
    // Create a custom next function to handle validation errors
    const customNext = (error?: any) => {
      if (error && error instanceof ValidationError) {
        const validationErrors = error.details.body || error.details.params || error.details.query || [];

        res.status(400).json({
          success: false,
          message: 'Validation failed',
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Request validation failed',
            details: validationErrors.map((err: any) => ({
              field: err.path?.join('.') || 'unknown',
              message: err.message,
              value: err.context?.value
            }))
          }
        });
        return;
      }

      // If no error or not a validation error, continue
      if (error) {
        next(error);
      } else {
        next();
      }
    };

    // Call the validation middleware with our custom next function
    validate(schema, {}, {})(req, res, customNext);
  };
}
