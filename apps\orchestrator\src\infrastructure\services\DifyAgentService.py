"""
Implementation of Dify Agent Service using the custom Dify client.
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional

# Import the local Dify client
try:
    from dify import DifyClient
    from dify.models.common import ResponseMode
    from dify.exceptions import DifyError, DifyAPIError, DifyTimeoutError, DifyValidationError
except ImportError as e:
    # Fallback for testing or when dify is not available
    print(f"Warning: Could not import dify library: {e}")

    # Create mock classes for testing
    class DifyClient:
        def __init__(self, *args, **kwargs):
            pass

    class ResponseMode:
        STREAMING = "streaming"
        BLOCKING = "blocking"

    class DifyError(Exception):
        pass

    class DifyAPIError(DifyError):
        pass

    class DifyTimeoutError(DifyError):
        pass

    class DifyValidationError(DifyError):
        pass


from ...application.interfaces import IDifyAgentService, AgentResponse
from ...domain.entities import AgentRequest, TenantConfig


class DifyAgentService(IDifyAgentService):
    """
    Implementation of IDifyAgentService using the custom Dify client.

    This service handles streaming responses from Dify internally
    but returns blocking responses to the use case.
    """

    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.dify.ai/v1",
        logger: Optional[logging.Logger] = None,
    ):
        self.api_key = api_key
        self.base_url = base_url
        self.logger = logger or logging.getLogger(__name__)
        self._client = None

    def _get_client(self) -> DifyClient:
        """Get or create Dify client instance."""
        if self._client is None:
            self._client = DifyClient(api_key=self.api_key, base_url=self.base_url)
        return self._client

    async def process_agent_request(
        self, request: AgentRequest, tenant_config: TenantConfig
    ) -> AgentResponse:
        """
        Process an agent request using Dify API.

        Args:
            request: The agent request from WhatsApp
            tenant_config: Tenant configuration for the request

        Returns:
            AgentResponse with the processed result

        Raises:
            DifyAPIError: For API-related errors
            DifyTimeoutError: For timeout errors
            DifyValidationError: For validation errors
        """
        start_time = time.time()

        try:
            self.logger.info(f"Processing Dify request for user: {request.get_user_identifier()}")

            client = self._get_client()

            # Prepare Dify API request
            inputs = tenant_config.to_dify_inputs()
            user_id = tenant_config.get_user_identifier()

            # Use streaming mode internally for better performance
            response_stream = client.send_chat_message(
                query=request.message_content,
                user=user_id,
                conversation_id=request.conversation_id,
                inputs=inputs,
                response_mode=ResponseMode.STREAMING,
                auto_generate_name=True,
            )

            # Collect streaming response into a complete response
            complete_response = await self._collect_streaming_response(response_stream)

            processing_time_ms = int((time.time() - start_time) * 1000)

            self.logger.info(
                f"Dify request processed in {processing_time_ms}ms, "
                f"conversation: {complete_response['conversation_id']}"
            )

            return AgentResponse(
                response_text=complete_response["answer"],
                conversation_id=complete_response["conversation_id"],
                dify_response_id=complete_response["message_id"],
                processing_time_ms=processing_time_ms,
                metadata={
                    "mode": complete_response.get("mode", "chat"),
                    "event": complete_response.get("event", "message"),
                    "task_id": complete_response.get("task_id", ""),
                },
            )

        except DifyError as e:
            self.logger.error(f"Dify API error: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error in Dify service: {e}", exc_info=True)
            raise DifyAPIError(f"Unexpected error: {e}")

    async def _collect_streaming_response(self, response_stream) -> Dict[str, Any]:
        """
        Collect streaming response into a complete response.

        Args:
            response_stream: Iterator of ChatStreamEvent objects

        Returns:
            Dictionary with complete response data
        """
        complete_answer = ""
        conversation_id = ""
        message_id = ""
        task_id = ""
        mode = ""
        event = ""

        try:
            # Run the streaming collection in a thread to avoid blocking
            loop = asyncio.get_event_loop()

            def collect_stream():
                nonlocal complete_answer, conversation_id, message_id, task_id, mode, event

                for stream_event in response_stream:
                    if hasattr(stream_event, "event"):
                        event = stream_event.event

                    if hasattr(stream_event, "conversation_id"):
                        conversation_id = stream_event.conversation_id

                    if hasattr(stream_event, "message_id"):
                        message_id = stream_event.message_id

                    if hasattr(stream_event, "task_id"):
                        task_id = stream_event.task_id

                    if hasattr(stream_event, "mode"):
                        mode = stream_event.mode

                    # Collect answer text
                    if hasattr(stream_event, "answer") and stream_event.answer:
                        complete_answer += stream_event.answer

                    # Handle different event types
                    if event == "message_end":
                        break

            # Execute stream collection in thread pool
            await loop.run_in_executor(None, collect_stream)

            return {
                "answer": complete_answer,
                "conversation_id": conversation_id,
                "message_id": message_id,
                "task_id": task_id,
                "mode": mode,
                "event": event,
            }

        except Exception as e:
            self.logger.error(f"Error collecting streaming response: {e}")
            raise DifyAPIError(f"Failed to collect streaming response: {e}")

    async def health_check(self) -> bool:
        """
        Perform a health check on the Dify service.

        Returns:
            True if service is healthy, False otherwise
        """
        try:
            client = self._get_client()
            return client.health_check()
        except Exception as e:
            self.logger.warning(f"Dify health check failed: {e}")
            return False

    def close(self):
        """Close the Dify client and clean up resources."""
        if self._client:
            self._client.close()
            self._client = None
