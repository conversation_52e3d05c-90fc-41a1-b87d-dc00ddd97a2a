"""
Unit tests for ConfigurationService.
"""

import sys
from pathlib import Path

# Ensure src is in path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Ensure current directory is in path for dify imports
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from domain.services import ConfigurationService
from domain.entities import TenantConfig


class TestConfigurationService:
    """Test cases for ConfigurationService."""

    def test_get_tenant_config_default(self):
        """Test getting default tenant configuration."""
        service = ConfigurationService()
        config = service.get_tenant_config()

        assert isinstance(config, TenantConfig)
        assert config.tenant_id == "demo_tenant_001"
        assert config.company_name == "EzyChat Demo Company"
        assert "sales assistant" in config.system_instructions.lower()
        assert "EzyChat Pro" in config.product_catalog

    def test_get_tenant_config_explicit_default(self):
        """Test getting tenant configuration with explicit default identifier."""
        service = ConfigurationService()
        config = service.get_tenant_config("default")

        assert isinstance(config, TenantConfig)
        assert config.tenant_id == "demo_tenant_001"

    def test_get_tenant_config_nonexistent_falls_back_to_default(self):
        """Test that nonexistent tenant identifier falls back to default."""
        service = ConfigurationService()
        config = service.get_tenant_config("nonexistent_tenant")

        # Should fall back to default configuration
        assert isinstance(config, TenantConfig)
        assert config.tenant_id == "demo_tenant_001"

    def test_tenant_config_to_dify_inputs(self):
        """Test conversion of tenant config to Dify inputs."""
        service = ConfigurationService()
        config = service.get_tenant_config()

        dify_inputs = config.to_dify_inputs()

        assert isinstance(dify_inputs, dict)
        assert "system_instructions" in dify_inputs
        assert "company_name" in dify_inputs
        assert "product_catalog" in dify_inputs
        assert dify_inputs["company_name"] == config.company_name

    def test_tenant_config_get_user_identifier(self):
        """Test getting user identifier from tenant config."""
        service = ConfigurationService()
        config = service.get_tenant_config()

        user_id = config.get_user_identifier()

        assert user_id == config.tenant_id
        assert user_id == "demo_tenant_001"

    def test_get_available_tenants(self):
        """Test getting list of available tenants."""
        service = ConfigurationService()
        tenants = service.get_available_tenants()

        assert isinstance(tenants, dict)
        assert "default" in tenants
        assert tenants["default"] == "EzyChat Demo Company"
