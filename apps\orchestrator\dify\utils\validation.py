"""
Validation Utilities for Dify SDK

Provides validation functions for API keys, user inputs, and other data
to ensure security and proper formatting before making API requests.
"""

import re
import uuid
from typing import Any, Dict, Optional, Union
from urllib.parse import urlparse

from ..exceptions import DifyValidationError


def validate_api_key(api_key: str) -> None:
    """
    Validate the format of a Dify API key.
    
    Args:
        api_key: API key to validate
        
    Raises:
        DifyValidationError: If the API key format is invalid
    """
    if not api_key:
        raise DifyValidationError("API key cannot be empty")
    
    if not isinstance(api_key, str):
        raise DifyValidationError("API key must be a string")
    
    # Remove whitespace
    api_key = api_key.strip()
    
    if len(api_key) < 10:
        raise DifyValidationError("API key appears to be too short")
    
    # Check for common invalid patterns
    if api_key.lower() in ['your-api-key', 'api-key', 'key', 'token']:
        raise DifyValidationError("API key appears to be a placeholder value")
    
    # Basic format validation (adjust based on actual Dify API key format)
    if not re.match(r'^[a-zA-Z0-9\-_\.]+$', api_key):
        raise DifyValidationError("API key contains invalid characters")


def validate_conversation_id(conversation_id: str) -> None:
    """
    Validate the format of a conversation ID.
    
    Args:
        conversation_id: Conversation ID to validate
        
    Raises:
        DifyValidationError: If the conversation ID format is invalid
    """
    if not conversation_id:
        raise DifyValidationError("Conversation ID cannot be empty")
    
    if not isinstance(conversation_id, str):
        raise DifyValidationError("Conversation ID must be a string")
    
    conversation_id = conversation_id.strip()
    
    # Check if it's a valid UUID format (common for conversation IDs)
    try:
        uuid.UUID(conversation_id)
    except ValueError:
        # If not a UUID, check for other valid formats
        if not re.match(r'^[a-zA-Z0-9\-_]+$', conversation_id):
            raise DifyValidationError("Conversation ID format is invalid")
        
        if len(conversation_id) < 8 or len(conversation_id) > 128:
            raise DifyValidationError("Conversation ID length is invalid")


def validate_user_id(user_id: str) -> None:
    """
    Validate the format of a user ID.
    
    Args:
        user_id: User ID to validate
        
    Raises:
        DifyValidationError: If the user ID format is invalid
    """
    if not user_id:
        raise DifyValidationError("User ID cannot be empty")
    
    if not isinstance(user_id, str):
        raise DifyValidationError("User ID must be a string")
    
    user_id = user_id.strip()
    
    if len(user_id) < 1 or len(user_id) > 256:
        raise DifyValidationError("User ID length must be between 1 and 256 characters")
    
    # Allow alphanumeric, hyphens, underscores, dots, and @ symbol
    if not re.match(r'^[a-zA-Z0-9\-_\.@]+$', user_id):
        raise DifyValidationError("User ID contains invalid characters")


def validate_base_url(base_url: str) -> str:
    """
    Validate and normalize a base URL.
    
    Args:
        base_url: Base URL to validate
        
    Returns:
        Normalized base URL
        
    Raises:
        DifyValidationError: If the base URL is invalid
    """
    if not base_url:
        raise DifyValidationError("Base URL cannot be empty")
    
    if not isinstance(base_url, str):
        raise DifyValidationError("Base URL must be a string")
    
    base_url = base_url.strip()
    
    # Parse the URL
    try:
        parsed = urlparse(base_url)
    except Exception as e:
        raise DifyValidationError(f"Invalid URL format: {e}")
    
    # Check scheme
    if parsed.scheme not in ['http', 'https']:
        raise DifyValidationError("Base URL must use http or https scheme")
    
    # Check hostname
    if not parsed.netloc:
        raise DifyValidationError("Base URL must include a hostname")
    
    # Remove trailing slash
    return base_url.rstrip('/')


def sanitize_input(text: str, max_length: int = 10000) -> str:
    """
    Sanitize user input text for API requests.
    
    Args:
        text: Input text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized text
        
    Raises:
        DifyValidationError: If the input is invalid
    """
    if not isinstance(text, str):
        raise DifyValidationError("Input must be a string")
    
    # Remove null bytes and other control characters
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    if len(text) > max_length:
        raise DifyValidationError(f"Input text exceeds maximum length of {max_length} characters")
    
    return text


def validate_file_size(file_size: int, max_size: int = 100 * 1024 * 1024) -> None:
    """
    Validate file size.
    
    Args:
        file_size: File size in bytes
        max_size: Maximum allowed size in bytes (default: 100MB)
        
    Raises:
        DifyValidationError: If the file size is invalid
    """
    if not isinstance(file_size, int):
        raise DifyValidationError("File size must be an integer")
    
    if file_size < 0:
        raise DifyValidationError("File size cannot be negative")
    
    if file_size == 0:
        raise DifyValidationError("File cannot be empty")
    
    if file_size > max_size:
        max_mb = max_size / (1024 * 1024)
        raise DifyValidationError(f"File size exceeds maximum allowed size of {max_mb:.1f}MB")


def validate_file_extension(filename: str, allowed_extensions: set = None) -> None:
    """
    Validate file extension.
    
    Args:
        filename: Name of the file
        allowed_extensions: Set of allowed extensions (e.g., {'.txt', '.pdf'})
        
    Raises:
        DifyValidationError: If the file extension is not allowed
    """
    if not filename:
        raise DifyValidationError("Filename cannot be empty")
    
    if not isinstance(filename, str):
        raise DifyValidationError("Filename must be a string")
    
    # Default allowed extensions for document upload
    if allowed_extensions is None:
        allowed_extensions = {
            '.txt', '.md', '.pdf', '.doc', '.docx', 
            '.csv', '.xlsx', '.json', '.xml'
        }
    
    # Extract extension
    extension = None
    if '.' in filename:
        extension = '.' + filename.split('.')[-1].lower()
    
    if extension not in allowed_extensions:
        allowed_list = ', '.join(sorted(allowed_extensions))
        raise DifyValidationError(
            f"File extension '{extension}' is not allowed. "
            f"Allowed extensions: {allowed_list}"
        )


def validate_inputs_dict(inputs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and sanitize an inputs dictionary.
    
    Args:
        inputs: Dictionary of input variables
        
    Returns:
        Validated and sanitized inputs dictionary
        
    Raises:
        DifyValidationError: If the inputs are invalid
    """
    if not isinstance(inputs, dict):
        raise DifyValidationError("Inputs must be a dictionary")
    
    validated_inputs = {}
    
    for key, value in inputs.items():
        # Validate key
        if not isinstance(key, str):
            raise DifyValidationError(f"Input key must be a string, got {type(key)}")
        
        if not key.strip():
            raise DifyValidationError("Input key cannot be empty")
        
        # Sanitize key
        clean_key = re.sub(r'[^\w\-_]', '', key.strip())
        if not clean_key:
            raise DifyValidationError(f"Input key '{key}' contains only invalid characters")
        
        # Validate and sanitize value based on type
        if isinstance(value, str):
            validated_inputs[clean_key] = sanitize_input(value)
        elif isinstance(value, (int, float, bool)):
            validated_inputs[clean_key] = value
        elif isinstance(value, (list, dict)):
            # For complex types, convert to string representation
            validated_inputs[clean_key] = str(value)
        else:
            validated_inputs[clean_key] = str(value)
    
    return validated_inputs


def validate_response_mode(response_mode: str) -> str:
    """
    Validate response mode parameter.
    
    Args:
        response_mode: Response mode to validate
        
    Returns:
        Validated response mode
        
    Raises:
        DifyValidationError: If the response mode is invalid
    """
    if not isinstance(response_mode, str):
        raise DifyValidationError("Response mode must be a string")
    
    response_mode = response_mode.lower().strip()
    
    valid_modes = {'blocking', 'streaming'}
    if response_mode not in valid_modes:
        raise DifyValidationError(
            f"Invalid response mode '{response_mode}'. "
            f"Valid modes: {', '.join(valid_modes)}"
        )
    
    return response_mode
