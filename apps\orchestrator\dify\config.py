"""
Dify SDK Configuration Management

Provides flexible configuration management for the Dify SDK with support
for environment variables, multi-tenant configurations, and integration
with the existing ezychat configuration patterns.
"""

import os
from typing import Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator
from dataclasses import dataclass


# Load environment variables from .env file if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not available, continue with system environment variables
    pass


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_retries: int = 3
    backoff_factor: float = 2.0
    retry_on_status: set = None
    
    def __post_init__(self):
        if self.retry_on_status is None:
            self.retry_on_status = {429, 500, 502, 503, 504}


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    requests_per_minute: int = 60
    burst_limit: int = 10


class DifyConfig(BaseModel):
    """
    Main configuration class for the Dify SDK.
    
    Supports both single-tenant and multi-tenant configurations with
    environment variable overrides and runtime configuration.
    """
    
    # Core API Configuration
    api_key: Optional[str] = Field(
        default=None,
        description="Default Dify API key"
    )
    base_url: str = Field(
        default="https://api.dify.ai/v1",
        description="Base URL for Dify API"
    )
    
    # Timeout Configuration
    timeout: float = Field(
        default=30.0,
        description="Request timeout in seconds"
    )
    connect_timeout: float = Field(
        default=10.0,
        description="Connection timeout in seconds"
    )
    read_timeout: float = Field(
        default=30.0,
        description="Read timeout in seconds"
    )
    
    # Retry Configuration
    max_retries: int = Field(
        default=3,
        description="Maximum number of retry attempts"
    )
    backoff_factor: float = Field(
        default=2.0,
        description="Exponential backoff factor for retries"
    )
    retry_on_status: set = Field(
        default_factory=lambda: {429, 500, 502, 503, 504},
        description="HTTP status codes to retry on"
    )
    
    # Rate Limiting Configuration
    rate_limit_rpm: int = Field(
        default=60,
        description="Rate limit in requests per minute"
    )
    rate_limit_burst: int = Field(
        default=10,
        description="Burst limit for rate limiting"
    )
    
    # Feature Flags
    enable_streaming: bool = Field(
        default=True,
        description="Enable streaming responses"
    )
    enable_retry: bool = Field(
        default=True,
        description="Enable automatic retries"
    )
    enable_rate_limiting: bool = Field(
        default=True,
        description="Enable client-side rate limiting"
    )
    
    # Logging Configuration
    log_requests: bool = Field(
        default=False,
        description="Log API requests (excluding sensitive data)"
    )
    log_responses: bool = Field(
        default=False,
        description="Log API responses"
    )
    
    # Multi-tenant Configuration
    tenant_configs: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Tenant-specific configuration overrides"
    )
    
    # User Agent
    user_agent: str = Field(
        default="dify-python-sdk/0.1.0 (ezychat)",
        description="User agent string for API requests"
    )
    
    class Config:
        env_prefix = "DIFY_"
        case_sensitive = False
    
    @field_validator('base_url')
    @classmethod
    def validate_base_url(cls, v):
        """Ensure base URL doesn't end with a slash."""
        return v.rstrip('/')
    
    @field_validator('timeout', 'connect_timeout', 'read_timeout')
    @classmethod
    def validate_timeouts(cls, v):
        """Ensure timeouts are positive."""
        if v <= 0:
            raise ValueError("Timeout values must be positive")
        return v
    
    @field_validator('max_retries')
    @classmethod
    def validate_max_retries(cls, v):
        """Ensure max_retries is non-negative."""
        if v < 0:
            raise ValueError("max_retries must be non-negative")
        return v
    
    def get_retry_config(self) -> RetryConfig:
        """Get retry configuration as a RetryConfig object."""
        return RetryConfig(
            max_retries=self.max_retries,
            backoff_factor=self.backoff_factor,
            retry_on_status=self.retry_on_status
        )
    
    def get_rate_limit_config(self) -> RateLimitConfig:
        """Get rate limit configuration as a RateLimitConfig object."""
        return RateLimitConfig(
            requests_per_minute=self.rate_limit_rpm,
            burst_limit=self.rate_limit_burst
        )
    
    def get_tenant_config(self, tenant_id: str) -> 'DifyConfig':
        """
        Get configuration for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            DifyConfig instance with tenant-specific overrides applied
        """
        if tenant_id not in self.tenant_configs:
            return self
        
        # Create a copy of the current config
        config_dict = self.dict()
        
        # Apply tenant-specific overrides
        tenant_overrides = self.tenant_configs[tenant_id]
        config_dict.update(tenant_overrides)
        
        # Remove tenant_configs to avoid recursion
        config_dict.pop('tenant_configs', None)
        
        return DifyConfig(**config_dict)
    
    def add_tenant_config(self, tenant_id: str, **overrides) -> None:
        """
        Add or update tenant-specific configuration.
        
        Args:
            tenant_id: Tenant identifier
            **overrides: Configuration overrides for the tenant
        """
        self.tenant_configs[tenant_id] = overrides
    
    @classmethod
    def from_env(cls) -> 'DifyConfig':
        """
        Create configuration from environment variables.
        
        Environment variables should be prefixed with DIFY_ (e.g., DIFY_API_KEY).
        """
        return cls(
            api_key=os.getenv('DIFY_API_KEY'),
            base_url=os.getenv('DIFY_BASE_URL', 'https://api.dify.ai/v1'),
            timeout=float(os.getenv('DIFY_TIMEOUT', '30.0')),
            max_retries=int(os.getenv('DIFY_MAX_RETRIES', '3')),
            rate_limit_rpm=int(os.getenv('DIFY_RATE_LIMIT_RPM', '60')),
            enable_streaming=os.getenv('DIFY_ENABLE_STREAMING', 'true').lower() == 'true',
            enable_retry=os.getenv('DIFY_ENABLE_RETRY', 'true').lower() == 'true',
            log_requests=os.getenv('DIFY_LOG_REQUESTS', 'false').lower() == 'true',
            log_responses=os.getenv('DIFY_LOG_RESPONSES', 'false').lower() == 'true',
        )


# Default configuration instance
default_config = DifyConfig.from_env()
