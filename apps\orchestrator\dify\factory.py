"""
Dify Client Factory

Factory class for creating Dify clients with multi-tenant support.
Integrates with the ezychat multi-tenant architecture.
"""

import logging
from typing import Optional, Dict, Any
from threading import Lock

from .client import DifyClient
from .config import DifyConfig, default_config
from .exceptions import DifyValidationError, DifyAuthenticationError


class DifyClientFactory:
    """
    Factory for creating and managing Dify clients with multi-tenant support.
    
    This factory provides a centralized way to create and cache Dify clients
    for different tenants, ensuring proper configuration isolation and
    resource management.
    
    Example:
        factory = DifyClientFactory(config)
        
        # Get client for specific tenant
        client = factory.get_client(tenant_id="tenant-123")
        
        # Get default client
        default_client = factory.get_client()
    """
    
    def __init__(
        self,
        config: Optional[DifyConfig] = None,
        logger: Optional[logging.Logger] = None,
        enable_caching: bool = True
    ):
        """
        Initialize the client factory.
        
        Args:
            config: Default DifyConfig instance. If not provided, uses default config.
            logger: Logger instance. If not provided, creates a default logger.
            enable_caching: Whether to cache client instances for reuse.
        """
        self.config = config or default_config
        self.logger = logger or logging.getLogger(__name__)
        self.enable_caching = enable_caching
        
        # Client cache for reuse
        self._client_cache: Dict[str, DifyClient] = {}
        self._cache_lock = Lock()
        
        # Async client cache (will be used when async_client.py is implemented)
        self._async_client_cache: Dict[str, Any] = {}
    
    def get_client(
        self,
        tenant_id: Optional[str] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        **kwargs
    ) -> DifyClient:
        """
        Get a Dify client for the specified tenant.
        
        Args:
            tenant_id: Tenant identifier. If None, returns default client.
            api_key: Override API key for this client.
            base_url: Override base URL for this client.
            **kwargs: Additional configuration overrides.
            
        Returns:
            DifyClient instance configured for the tenant
            
        Raises:
            DifyAuthenticationError: If no valid API key is available
            DifyValidationError: If configuration is invalid
        """
        # Create cache key
        cache_key = self._create_cache_key(tenant_id, api_key, base_url, **kwargs)
        
        # Check cache first
        if self.enable_caching:
            with self._cache_lock:
                if cache_key in self._client_cache:
                    cached_client = self._client_cache[cache_key]
                    # Verify the cached client is still valid
                    if self._is_client_valid(cached_client):
                        return cached_client
                    else:
                        # Remove invalid client from cache
                        del self._client_cache[cache_key]
        
        # Create new client
        client = self._create_client(tenant_id, api_key, base_url, **kwargs)
        
        # Cache the client if caching is enabled
        if self.enable_caching:
            with self._cache_lock:
                self._client_cache[cache_key] = client
        
        return client
    
    def _create_client(
        self,
        tenant_id: Optional[str] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        **kwargs
    ) -> DifyClient:
        """
        Create a new Dify client instance.
        
        Args:
            tenant_id: Tenant identifier
            api_key: Override API key
            base_url: Override base URL
            **kwargs: Additional configuration overrides
            
        Returns:
            New DifyClient instance
        """
        # Get tenant-specific configuration
        if tenant_id:
            client_config = self.config.get_tenant_config(tenant_id)
        else:
            client_config = self.config
        
        # Apply parameter overrides
        config_overrides = {}
        if api_key:
            config_overrides['api_key'] = api_key
        if base_url:
            config_overrides['base_url'] = base_url
        config_overrides.update(kwargs)
        
        # Create client with merged configuration
        if config_overrides:
            config_dict = client_config.dict()
            config_dict.update(config_overrides)
            final_config = DifyConfig(**config_dict)
        else:
            final_config = client_config
        
        self.logger.debug(f"Creating Dify client for tenant: {tenant_id or 'default'}")
        
        return DifyClient(
            api_key=final_config.api_key,
            base_url=final_config.base_url,
            config=final_config,
            logger=self.logger
        )
    
    def _create_cache_key(
        self,
        tenant_id: Optional[str] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Create a cache key for the client configuration.
        
        Args:
            tenant_id: Tenant identifier
            api_key: API key
            base_url: Base URL
            **kwargs: Additional configuration
            
        Returns:
            Cache key string
        """
        # Create a deterministic key based on configuration
        key_parts = [
            f"tenant:{tenant_id or 'default'}",
            f"api_key:{api_key[:8] if api_key else 'default'}...",
            f"base_url:{base_url or 'default'}",
        ]
        
        # Add other configuration parameters
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}:{v}")
        
        return "|".join(key_parts)
    
    def _is_client_valid(self, client: DifyClient) -> bool:
        """
        Check if a cached client is still valid.
        
        Args:
            client: DifyClient instance to validate
            
        Returns:
            True if the client is valid, False otherwise
        """
        try:
            # Check if the client's HTTP client is still open
            if not hasattr(client, 'http_client') or client.http_client.is_closed:
                return False
            
            # Could add additional validation here (e.g., test API call)
            return True
        except Exception as e:
            self.logger.warning(f"Client validation failed: {e}")
            return False
    
    def add_tenant_config(
        self,
        tenant_id: str,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        **config_overrides
    ) -> None:
        """
        Add or update configuration for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier
            api_key: Tenant-specific API key
            base_url: Tenant-specific base URL
            **config_overrides: Additional configuration overrides
        """
        overrides = {}
        if api_key:
            overrides['api_key'] = api_key
        if base_url:
            overrides['base_url'] = base_url
        overrides.update(config_overrides)
        
        self.config.add_tenant_config(tenant_id, **overrides)
        
        # Invalidate cached clients for this tenant
        self._invalidate_tenant_cache(tenant_id)
        
        self.logger.info(f"Updated configuration for tenant: {tenant_id}")
    
    def remove_tenant_config(self, tenant_id: str) -> None:
        """
        Remove configuration for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier
        """
        if tenant_id in self.config.tenant_configs:
            del self.config.tenant_configs[tenant_id]
            self._invalidate_tenant_cache(tenant_id)
            self.logger.info(f"Removed configuration for tenant: {tenant_id}")
    
    def _invalidate_tenant_cache(self, tenant_id: str) -> None:
        """
        Invalidate cached clients for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier
        """
        if not self.enable_caching:
            return
        
        with self._cache_lock:
            # Find and remove all cache entries for this tenant
            keys_to_remove = [
                key for key in self._client_cache.keys()
                if key.startswith(f"tenant:{tenant_id}|")
            ]
            
            for key in keys_to_remove:
                client = self._client_cache.pop(key, None)
                if client:
                    try:
                        client.close()
                    except Exception as e:
                        self.logger.warning(f"Error closing client: {e}")
    
    def clear_cache(self) -> None:
        """Clear all cached clients."""
        if not self.enable_caching:
            return
        
        with self._cache_lock:
            for client in self._client_cache.values():
                try:
                    client.close()
                except Exception as e:
                    self.logger.warning(f"Error closing client: {e}")
            
            self._client_cache.clear()
            self._async_client_cache.clear()
        
        self.logger.info("Cleared all cached clients")
    
    def get_tenant_list(self) -> list:
        """
        Get a list of configured tenant IDs.
        
        Returns:
            List of tenant IDs
        """
        return list(self.config.tenant_configs.keys())
    
    def health_check(self, tenant_id: Optional[str] = None) -> bool:
        """
        Perform a health check for a specific tenant or default configuration.
        
        Args:
            tenant_id: Tenant identifier. If None, checks default configuration.
            
        Returns:
            True if the client can connect successfully, False otherwise
        """
        try:
            client = self.get_client(tenant_id=tenant_id)
            return client.health_check()
        except Exception as e:
            self.logger.error(f"Health check failed for tenant {tenant_id}: {e}")
            return False
    
    def __del__(self):
        """Cleanup when the factory is destroyed."""
        try:
            self.clear_cache()
        except Exception:
            pass


# Global factory instance for convenience
_global_factory: Optional[DifyClientFactory] = None


def get_global_factory() -> DifyClientFactory:
    """
    Get the global DifyClientFactory instance.
    
    Returns:
        Global DifyClientFactory instance
    """
    global _global_factory
    if _global_factory is None:
        _global_factory = DifyClientFactory()
    return _global_factory


def set_global_factory(factory: DifyClientFactory) -> None:
    """
    Set the global DifyClientFactory instance.
    
    Args:
        factory: DifyClientFactory instance to set as global
    """
    global _global_factory
    _global_factory = factory
