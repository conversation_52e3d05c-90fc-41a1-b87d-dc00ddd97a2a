# Use the official AWS Lambda Python 3.12 base image
FROM public.ecr.aws/lambda/python:3.12

# Set working directory
WORKDIR ${LAMBDA_TASK_ROOT}

# Copy requirements first for better caching
COPY requirements.txt ${LAMBDA_TASK_ROOT}/

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the custom Dify library
COPY dify/ ${LAMBDA_TASK_ROOT}/dify/

# Copy source code
COPY src/ ${LAMBDA_TASK_ROOT}/src/

# Set Python path to include current directory
ENV PYTHONPATH="${LAMBDA_TASK_ROOT}:${PYTHONPATH}"

# Set the CMD to the Lambda handler
CMD ["src.presentation.lambda_handler.lambda_handler"]
