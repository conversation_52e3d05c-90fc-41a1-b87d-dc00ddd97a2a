"""
Pytest configuration for orchestrator tests.

This file sets up common configuration for all tests in the orchestrator project.
"""

import os
import sys
from pathlib import Path


def load_env_file(env_file_path: Path) -> None:
    """Load environment variables from a .env file."""
    if not env_file_path.exists():
        return

    with open(env_file_path, "r") as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                os.environ[key.strip()] = value.strip()


def setup_test_environment():
    """Set up environment variables based on test type."""
    # Check if we're running integration tests by looking at pytest markers
    is_integration_test = (
        any("integration" in arg for arg in sys.argv)
        or "-m integration" in " ".join(sys.argv)
        or "--integration" in sys.argv
        or "integration" in os.getenv("PYTEST_CURRENT_TEST", "")
        or os.getenv("TEST_TYPE") == "integration"
    )

    if is_integration_test:
        # Load real environment variables from .env.local for integration tests
        current_dir = Path(__file__).parent.parent
        env_local_path = current_dir / ".env.local"
        if env_local_path.exists():
            load_env_file(env_local_path)
            print(f"Loaded integration test environment from {env_local_path}")
        else:
            print(f"Warning: .env.local not found at {env_local_path}")
            # Set a placeholder that will cause tests to skip if no real API key
            os.environ.setdefault("DIFY_API_KEY", "")
    else:
        # Set default/mock values for unit tests only
        os.environ.setdefault("DIFY_API_KEY", "app-test1234567890abcdef1234567890ab")
        os.environ.setdefault("DIFY_BASE_URL", "https://api.dify.ai/v1")
        os.environ.setdefault("LOG_LEVEL", "INFO")
        os.environ.setdefault("PYTHON_ENV", "test")


# Set up environment before importing any modules
setup_test_environment()

# Add the current directory to Python path for testing
# This allows importing the local dify library during tests
current_dir = Path(__file__).parent.parent

if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Also add the src directory to the path
src_path = current_dir / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))


# Pytest fixtures
import pytest


@pytest.fixture(scope="session")
def integration_env_check():
    """Fixture to ensure integration tests have proper environment setup."""
    api_key = os.getenv("DIFY_API_KEY")
    if not api_key or api_key.startswith("test-") or api_key.startswith("app-test"):
        pytest.skip("Integration tests require a real DIFY_API_KEY from .env.local")
    return api_key


# Import fixtures to make them available to all tests
try:
    from .fixtures import (
        sample_tenant_config,
        sample_agent_request,
        sample_agent_request_with_conversation,
        sample_api_gateway_event,
        sample_api_gateway_event_with_conversation,
        sample_lambda_context,
    )
except ImportError:
    # If fixtures can't be imported, they won't be available to tests
    pass
