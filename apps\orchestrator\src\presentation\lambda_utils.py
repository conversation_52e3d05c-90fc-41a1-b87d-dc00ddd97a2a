"""
Utility functions for Lambda handler that can be tested independently.
"""

import json
from typing import Dict, Any, Optional

from ..domain.entities import AgentRequest


def create_response(
    status_code: int, body: Dict[str, Any], headers: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """
    Create a properly formatted API Gateway response.

    Args:
        status_code: HTTP status code
        body: Response body dictionary
        headers: Optional additional headers

    Returns:
        API Gateway response dictionary
    """
    default_headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
    }

    if headers:
        default_headers.update(headers)

    return {
        "statusCode": status_code,
        "headers": default_headers,
        "body": json.dumps(body, ensure_ascii=False),
    }


def parse_request_body(event: Dict[str, Any]) -> AgentRequest:
    """
    Parse the request body into an AgentRequest entity.

    Args:
        event: API Gateway event

    Returns:
        AgentRequest instance

    Raises:
        ValueError: If request body is invalid
    """
    try:
        # Parse JSON body
        if isinstance(event.get("body"), str):
            body = json.loads(event["body"])
        else:
            body = event.get("body", {})

        # Validate required fields
        required_fields = ["phone_number", "message_content", "message_id"]
        for field in required_fields:
            if field not in body:
                raise ValueError(f"Missing required field: {field}")

        # Create AgentRequest entity
        return AgentRequest(
            phone_number=body["phone_number"],
            message_content=body["message_content"],
            conversation_id=body.get("conversation_id"),
            message_id=body["message_id"],
        )

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in request body: {e}")
    except Exception as e:
        raise ValueError(f"Failed to parse request: {e}")
