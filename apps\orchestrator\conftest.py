"""
Pytest configuration for the orchestrator lambda.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path for testing
# This allows importing the local dify library during tests
current_dir = Path(__file__).parent

if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Also add the src directory to the path
src_path = current_dir / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))
