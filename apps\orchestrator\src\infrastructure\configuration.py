"""
Configuration management for the orchestrator lambda.
"""

import os
import logging
from pydantic import BaseModel, Field


class OrchestratorConfig(BaseModel):
    """Configuration for the orchestrator lambda."""

    # Dify API Configuration
    dify_api_key: str = Field(description="Dify API key for authentication")
    dify_base_url: str = Field(
        default="https://api.dify.ai/v1", description="Base URL for Dify API"
    )

    # Lambda Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    environment: str = Field(default="production", description="Environment name")

    # Timeout Configuration
    dify_timeout_seconds: int = Field(
        default=30, description="Timeout for Dify API calls in seconds"
    )

    @classmethod
    def from_environment(cls) -> "OrchestratorConfig":
        """
        Create configuration from environment variables.

        Returns:
            OrchestratorConfig instance

        Raises:
            ValueError: If required environment variables are missing
        """
        dify_api_key = os.getenv("DIFY_API_KEY")
        if not dify_api_key:
            raise ValueError("DIFY_API_KEY environment variable is required")

        return cls(
            dify_api_key=dify_api_key,
            dify_base_url=os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1"),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            environment=os.getenv("PYTHON_ENV", "production"),
            dify_timeout_seconds=int(os.getenv("DIFY_TIMEOUT_SECONDS", "30")),
        )


def setup_logging(config: OrchestratorConfig) -> logging.Logger:
    """
    Set up logging configuration.

    Args:
        config: Orchestrator configuration

    Returns:
        Configured logger instance
    """
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        force=True,
    )

    # Create logger for the orchestrator
    logger = logging.getLogger("orchestrator")

    # Add environment context
    logger.info(f"Orchestrator starting in {config.environment} environment")
    logger.info(f"Log level set to {config.log_level}")

    return logger
