"""
<PERSON>ck WhatsApp Manager for testing.
"""

from typing import Dict, Any


class MockWhatsAppManager:
    """
    Mock WhatsApp Manager for testing the orchestrator lambda.

    This mock simulates the WhatsApp Manager service that would
    send requests to the orchestrator lambda.
    """

    def __init__(self):
        self.sent_messages = []
        self.received_responses = []

    def send_message_to_orchestrator(
        self, phone_number: str, message_content: str, message_id: str, conversation_id: str = None
    ) -> Dict[str, Any]:
        """
        Mock sending a message to the orchestrator lambda.

        Args:
            phone_number: WhatsApp phone number
            message_content: Message content
            message_id: Unique message ID
            conversation_id: Optional conversation ID

        Returns:
            Mock orchestrator response
        """
        # Record the sent message
        sent_message = {
            "phone_number": phone_number,
            "message_content": message_content,
            "message_id": message_id,
            "conversation_id": conversation_id,
        }
        self.sent_messages.append(sent_message)

        # Return mock successful response
        mock_response = {
            "success": True,
            "conversation_id": conversation_id or "mock_conv_001",
            "response_text": "Thank you for your message! This is a mock response from the orchestrator.",
            "tenant_id": "demo_tenant_001",
            "metadata": {
                "processing_time_ms": 1200,
                "dify_response_id": "mock_dify_msg_001",
                "company_name": "EzyChat Demo Company",
            },
        }

        self.received_responses.append(mock_response)
        return mock_response

    def get_sent_messages(self) -> list:
        """Get list of messages sent to orchestrator."""
        return self.sent_messages.copy()

    def get_received_responses(self) -> list:
        """Get list of responses received from orchestrator."""
        return self.received_responses.copy()

    def clear_history(self):
        """Clear message and response history."""
        self.sent_messages.clear()
        self.received_responses.clear()


def create_mock_whatsapp_request(
    phone_number: str = "+1234567890",
    message_content: str = "Hello, I need help",
    message_id: str = "mock_msg_001",
    conversation_id: str = None,
) -> Dict[str, Any]:
    """
    Create a mock WhatsApp request for testing.

    Args:
        phone_number: WhatsApp phone number
        message_content: Message content
        message_id: Unique message ID
        conversation_id: Optional conversation ID

    Returns:
        Mock WhatsApp request dictionary
    """
    request = {
        "phone_number": phone_number,
        "message_content": message_content,
        "message_id": message_id,
    }

    if conversation_id:
        request["conversation_id"] = conversation_id

    return request
