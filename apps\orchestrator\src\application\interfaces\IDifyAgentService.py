"""
Interface for Dify Agent Service.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from ...domain.entities import TenantConfig, AgentRequest


class AgentResponse:
    """Response from the Dify agent."""

    def __init__(
        self,
        response_text: str,
        conversation_id: str,
        dify_response_id: str,
        processing_time_ms: int,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        self.response_text = response_text
        self.conversation_id = conversation_id
        self.dify_response_id = dify_response_id
        self.processing_time_ms = processing_time_ms
        self.metadata = metadata or {}


class IDifyAgentService(ABC):
    """
    Interface for Dify Agent Service.

    Defines the contract for interacting with the Dify API
    to process agent requests.
    """

    @abstractmethod
    async def process_agent_request(
        self, request: AgentRequest, tenant_config: TenantConfig
    ) -> AgentResponse:
        """
        Process an agent request using Dify API.

        Args:
            request: The agent request from WhatsApp
            tenant_config: Tenant configuration for the request

        Returns:
            AgentResponse with the processed result

        Raises:
            DifyAPIError: For API-related errors
            DifyTimeoutError: For timeout errors
            DifyValidationError: For validation errors
        """
        pass

    @abstractmethod
    async def health_check(self) -> bool:
        """
        Perform a health check on the Dify service.

        Returns:
            True if service is healthy, False otherwise
        """
        pass
