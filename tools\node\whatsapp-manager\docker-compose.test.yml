version: '3.8'

services:
  whatsapp-manager-test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      - NODE_ENV=test
      - PORT=3000
      - HOST=0.0.0.0
      - AWS_REGION=ap-southeast-1
      - DYNAMODB_ENDPOINT=http://dynamodb-local-test:8000
      - DYNAMODB_TABLE_NAME=WhatsAppSessions-test
      - JWT_SECRET=test-secret-key-123-minimum-32-characters-required
      - QR_TOKEN_EXPIRY_SEC=300
      - CORS_ORIGINS=*
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX_REQUESTS=1000
      - LOG_LEVEL=info
      - LOG_FORMAT=json
    depends_on:
      dynamodb-local-test:
        condition: service_healthy
    command: ["npm", "test"]
    networks:
      - whatsapp-test-network

  dynamodb-local-test:
    image: amazon/dynamodb-local:latest
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-inMemory"]
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
    networks:
      - whatsapp-test-network

networks:
  whatsapp-test-network:
    name: whatsapp-api-test-network
    driver: bridge
