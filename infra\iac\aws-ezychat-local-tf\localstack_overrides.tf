# LocalStack-Specific Resource Overrides
# This file contains LocalStack-specific configurations that override or supplement the core module

# LocalStack Route 53 Hosted Zone (for local development)
# Create this first to satisfy the core module's data source lookup
resource "aws_route53_zone" "localhost_domain" {
  name = "localhost"

  tags = {
    Environment = "localstack"
    Purpose     = "local-development"
  }
}

# Create a simple A record for testing
resource "aws_route53_record" "localhost_test" {
  zone_id = aws_route53_zone.localhost_domain.zone_id
  name    = "api.local.localhost"
  type    = "A"
  ttl     = 300
  records = ["127.0.0.1"]
}

# LocalStack ECR Repository for Lambda Container Images
resource "aws_ecr_repository" "lambda_document_ingestion" {
  name                 = "lambda-document-ingestion"
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = false # Simplified for LocalStack
  }

  tags = {
    Service     = "document-ingestion"
    Environment = "localstack"
  }
}

# ECR Lifecycle Policy (simplified for LocalStack)
resource "aws_ecr_lifecycle_policy" "lambda_document_ingestion" {
  repository = aws_ecr_repository.lambda_document_ingestion.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 5 images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = 5
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# LocalStack S3 Bucket for Document Processing
resource "aws_s3_bucket" "document_processing_local" {
  bucket        = "ezychat-documents-local"
  force_destroy = true # Allow easy cleanup in LocalStack

  tags = {
    Environment = "localstack"
    Purpose     = "document-processing"
  }
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "document_processing_local" {
  bucket = aws_s3_bucket.document_processing_local.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Notification for Lambda Trigger
resource "aws_s3_bucket_notification" "document_processing_notification" {
  bucket = aws_s3_bucket.document_processing_local.id

  lambda_function {
    lambda_function_arn = aws_lambda_function.document_ingestion_local.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = ""
    filter_suffix       = ".csv"
  }

  depends_on = [aws_lambda_permission.allow_s3_invoke]
}

# Lambda Permission for S3 to Invoke Function
resource "aws_lambda_permission" "allow_s3_invoke" {
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.document_ingestion_local.function_name
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.document_processing_local.arn
}

# LocalStack Lambda Function (Container-based)
resource "aws_lambda_function" "document_ingestion_local" {
  function_name = "document-ingestion-local"
  role          = aws_iam_role.lambda_execution_role_local.arn

  # Container image configuration
  package_type = "Image"
  image_uri    = "${aws_ecr_repository.lambda_document_ingestion.repository_url}:latest"

  timeout     = 300
  memory_size = 1024

  environment {
    variables = {
      PYTHON_ENV                    = "localstack"
      LOG_LEVEL                     = "DEBUG"
      ENVIRONMENT                   = "local"
      DOCUMENT_BUCKET_NAME          = aws_s3_bucket.document_processing_local.bucket
      OPENAI_EMBEDDING_MODEL        = "text-embedding-3-small"
      OPENAI_EMBEDDING_DIMENSIONS   = "512"
      MAX_FILE_SIZE_BYTES          = "52428800"
      MAX_ROWS                     = "100000"
      BATCH_SIZE                   = "100"
      RETRY_ATTEMPTS               = "3"
      RETRY_MIN_WAIT               = "1"
      RETRY_MAX_WAIT               = "60"
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.lambda_execution_role_policy_local,
    aws_cloudwatch_log_group.lambda_log_group_local,
  ]

  tags = {
    Environment = "localstack"
    Service     = "document-ingestion"
  }
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_log_group_local" {
  name              = "/aws/lambda/document-ingestion-local"
  retention_in_days = 7 # Short retention for LocalStack

  tags = {
    Environment = "localstack"
    Service     = "document-ingestion"
  }
}

# IAM Role for Lambda Execution
resource "aws_iam_role" "lambda_execution_role_local" {
  name = "lambda-execution-role-local"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = "localstack"
    Service     = "document-ingestion"
  }
}

# IAM Policy for Lambda Execution
resource "aws_iam_role_policy" "lambda_execution_policy_local" {
  name = "lambda-execution-policy-local"
  role = aws_iam_role.lambda_execution_role_local.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.document_processing_local.arn,
          "${aws_s3_bucket.document_processing_local.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath"
        ]
        Resource = "arn:aws:ssm:*:*:parameter/ezychat/document-ingestion/*"
      }
    ]
  })
}

# Attach AWS managed policy for basic Lambda execution
resource "aws_iam_role_policy_attachment" "lambda_execution_role_policy_local" {
  role       = aws_iam_role.lambda_execution_role_local.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# Outputs for LocalStack-specific resources
output "localstack_ecr_repository_url" {
  description = "LocalStack ECR repository URL for document ingestion"
  value       = aws_ecr_repository.lambda_document_ingestion.repository_url
}

output "localstack_s3_bucket_name" {
  description = "LocalStack S3 bucket for document processing"
  value       = aws_s3_bucket.document_processing_local.bucket
}

output "localstack_lambda_function_name" {
  description = "LocalStack Lambda function name"
  value       = aws_lambda_function.document_ingestion_local.function_name
}
