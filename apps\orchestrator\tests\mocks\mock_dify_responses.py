"""
Mock Dify API responses for testing.
"""

from typing import Iterator, Dict, Any
from unittest.mock import Mock


class MockChatStreamEvent:
    """Mock chat stream event for testing."""

    def __init__(
        self,
        event: str = "message",
        answer: str = "",
        conversation_id: str = "test_conv_001",
        message_id: str = "test_msg_001",
        task_id: str = "test_task_001",
        mode: str = "chat",
    ):
        self.event = event
        self.answer = answer
        self.conversation_id = conversation_id
        self.message_id = message_id
        self.task_id = task_id
        self.mode = mode


def create_mock_streaming_response() -> Iterator[MockChatStreamEvent]:
    """Create a mock streaming response for testing."""
    events = [
        MockChatStreamEvent(
            event="message",
            answer="Hello! I'm here to help you with our products. ",
            conversation_id="test_conv_001",
            message_id="test_msg_001",
            task_id="test_task_001",
        ),
        MockChatStreamEvent(
            event="message",
            answer="We offer EzyChat Pro for $99/month and EzyChat Basic for $49/month. ",
            conversation_id="test_conv_001",
            message_id="test_msg_001",
            task_id="test_task_001",
        ),
        MockChatStreamEvent(
            event="message",
            answer="Which one would you like to know more about?",
            conversation_id="test_conv_001",
            message_id="test_msg_001",
            task_id="test_task_001",
        ),
        MockChatStreamEvent(
            event="message_end",
            answer="",
            conversation_id="test_conv_001",
            message_id="test_msg_001",
            task_id="test_task_001",
        ),
    ]

    for event in events:
        yield event


def create_mock_dify_client():
    """Create a mock Dify client for testing."""
    mock_client = Mock()

    # Mock send_chat_message to return streaming response
    mock_client.send_chat_message.return_value = create_mock_streaming_response()

    # Mock health_check
    mock_client.health_check.return_value = True

    # Mock close method
    mock_client.close.return_value = None

    return mock_client


def create_mock_agent_response() -> Dict[str, Any]:
    """Create a mock agent response for testing."""
    return {
        "response_text": "Hello! I'm here to help you with our products. We offer EzyChat Pro for $99/month and EzyChat Basic for $49/month. Which one would you like to know more about?",
        "conversation_id": "test_conv_001",
        "dify_response_id": "test_msg_001",
        "processing_time_ms": 1500,
        "metadata": {"mode": "chat", "event": "message_end", "task_id": "test_task_001"},
    }
