"""
Dependency injection configuration for the orchestrator lambda.
"""

import logging
from dependency_injector import containers, providers

from .configuration import OrchestratorConfig, setup_logging
from .services import DifyAgentService
from ..domain.services import ConfigurationService
from ..application.use_cases import ProcessAgentRequestUseCase


class Container(containers.DeclarativeContainer):
    """
    Dependency injection container for the orchestrator lambda.

    This container manages all dependencies and their lifecycles
    using the dependency-injector framework.
    """

    # Configuration
    config = providers.Singleton(OrchestratorConfig.from_environment)

    # Logging
    logger = providers.Singleton(setup_logging, config=config)

    # Domain Services
    configuration_service = providers.Singleton(ConfigurationService)

    # Infrastructure Services
    dify_agent_service = providers.Singleton(
        DifyAgentService,
        api_key=config.provided.dify_api_key,
        base_url=config.provided.dify_base_url,
        logger=logger,
    )

    # Application Use Cases
    process_agent_request_use_case = providers.Factory(
        ProcessAgentRequestUseCase,
        dify_agent_service=dify_agent_service,
        configuration_service=configuration_service,
        logger=logger,
    )


# Global container instance
container = Container()


def get_container() -> Container:
    """
    Get the global dependency injection container.

    Returns:
        Container instance
    """
    return container


def wire_container():
    """Wire the container for dependency injection."""
    container.wire(modules=[__name__])


def shutdown_container():
    """Shutdown the container and clean up resources."""
    try:
        # Close Dify agent service if it exists
        if hasattr(container, "dify_agent_service"):
            dify_service = container.dify_agent_service()
            if hasattr(dify_service, "close"):
                dify_service.close()
    except Exception as e:
        # Log error but don't raise to avoid masking other shutdown issues
        logger = logging.getLogger(__name__)
        logger.warning(f"Error during container shutdown: {e}")

    container.shutdown_resources()
