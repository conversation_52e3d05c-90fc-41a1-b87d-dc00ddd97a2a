# Makefile for LocalStack Development Environment
# This provides automation for the complete LocalStack workflow

# Variables
LOCALSTACK_ENDPOINT = http://localhost:4566
AWS_REGION = us-east-1
ECR_REGISTRY = 000000000000.dkr.ecr.$(AWS_REGION).localhost.localstack.cloud:4566
IMAGE_NAME = lambda-document-ingestion
IMAGE_TAG = latest
LAMBDA_DIR = ../../../apps/document-ingestion
FUNCTION_NAME = document-ingestion-local
BUCKET_NAME = ezychat-documents-local

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

# Default target
.PHONY: help
help:
	@echo "$(BLUE)EzyChat LocalStack Development Environment$(NC)"
	@echo ""
	@echo "$(YELLOW)LocalStack Management:$(NC)"
	@echo "  localstack-start     Start LocalStack services"
	@echo "  localstack-stop      Stop LocalStack services"
	@echo "  localstack-status    Check LocalStack health"
	@echo "  localstack-logs      View LocalStack logs"
	@echo ""
	@echo "$(YELLOW)Infrastructure:$(NC)"
	@echo "  tflocal-init         Initialize Terraform for LocalStack"
	@echo "  tflocal-plan         Plan infrastructure deployment"
	@echo "  tflocal-apply        Deploy infrastructure to LocalStack"
	@echo "  tflocal-destroy      Destroy LocalStack infrastructure"
	@echo ""
	@echo "$(YELLOW)Lambda Development:$(NC)"
	@echo "  lambda-build         Build Lambda container image"
	@echo "  lambda-push          Push image to LocalStack ECR"
	@echo "  lambda-deploy        Build, push, and deploy Lambda"
	@echo "  lambda-update        Update Lambda function code"
	@echo "  lambda-logs          View Lambda function logs"
	@echo ""
	@echo "$(YELLOW)Testing:$(NC)"
	@echo "  test-setup           Setup test data and parameters"
	@echo "  test-s3-event        Test S3 event processing"
	@echo "  test-api             Test API Gateway endpoints"
	@echo "  test-full            Run full test suite"
	@echo ""
	@echo "$(YELLOW)Utilities:$(NC)"
	@echo "  cleanup              Clean up all resources"
	@echo "  reset                Reset entire LocalStack environment"
	@echo "  dev-setup            Complete development environment setup"

# ============================================================================
# LocalStack Management
# ============================================================================

.PHONY: localstack-start
localstack-start:
	@echo "$(GREEN)Starting LocalStack services...$(NC)"
	@echo "$(YELLOW)Note: Make sure LOCALSTACK_API_KEY environment variable is set$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Waiting for LocalStack to be ready...$(NC)"
	@echo "$(YELLOW)Checking LocalStack health (this may take a minute)...$(NC)"
	@powershell -Command "for ($$i = 0; $$i -lt 30; $$i++) { try { $$response = Invoke-RestMethod -Uri 'http://localhost:4566/_localstack/health' -TimeoutSec 2; if ($$response -match 'running') { Write-Host 'LocalStack is ready!'; exit 0 } } catch { } Start-Sleep 2 }"
	@echo "$(GREEN)LocalStack should be ready! Check with 'make localstack-status'$(NC)"

.PHONY: localstack-stop
localstack-stop:
	@echo "$(YELLOW)Stopping LocalStack services...$(NC)"
	docker-compose down

.PHONY: localstack-status
localstack-status:
	@echo "$(BLUE)LocalStack Health Status:$(NC)"
	@curl -s http://localhost:4566/_localstack/health | jq . || echo "$(RED)LocalStack is not running$(NC)"

.PHONY: localstack-logs
localstack-logs:
	@echo "$(BLUE)LocalStack Logs:$(NC)"
	docker-compose logs -f localstack

# ============================================================================
# Terraform Operations
# ============================================================================

.PHONY: tflocal-init
tflocal-init:
	@echo "$(GREEN)Initializing Terraform for LocalStack...$(NC)"
	tflocal init

.PHONY: tflocal-plan
tflocal-plan:
	@echo "$(GREEN)Planning infrastructure deployment...$(NC)"
	tflocal plan

.PHONY: tflocal-create-zone
tflocal-create-zone:
	@echo "$(GREEN)Creating Route 53 hosted zone for LocalStack...$(NC)"
	tflocal apply -auto-approve -target=aws_route53_zone.localhost_domain

.PHONY: tflocal-apply
tflocal-apply:
	@echo "$(GREEN)Deploying infrastructure to LocalStack...$(NC)"
	@echo "$(YELLOW)Step 1: Creating Route 53 hosted zone first...$(NC)"
	tflocal apply -auto-approve -target=aws_route53_zone.localhost_domain
	@echo "$(YELLOW)Step 2: Deploying remaining infrastructure...$(NC)"
	tflocal apply -auto-approve

.PHONY: tflocal-destroy
tflocal-destroy:
	@echo "$(YELLOW)Destroying LocalStack infrastructure...$(NC)"
	tflocal destroy -auto-approve

# ============================================================================
# Lambda Development Workflow
# ============================================================================

.PHONY: lambda-build
lambda-build:
	@echo "$(GREEN)Building Lambda container image...$(NC)"
	cd $(LAMBDA_DIR) && docker build -t $(IMAGE_NAME):$(IMAGE_TAG) .

.PHONY: ecr-login
ecr-login:
	@echo "$(GREEN)Logging into LocalStack ECR...$(NC)"
	aws --endpoint-url=$(LOCALSTACK_ENDPOINT) ecr get-login-password --region $(AWS_REGION) | \
	docker login --username AWS --password-stdin $(ECR_REGISTRY)

.PHONY: ecr-create-repo
ecr-create-repo:
	@echo "$(GREEN)Creating ECR repository in LocalStack...$(NC)"
	aws --endpoint-url=$(LOCALSTACK_ENDPOINT) ecr create-repository \
		--repository-name $(IMAGE_NAME) \
		--region $(AWS_REGION) || true

.PHONY: lambda-tag-and-push
lambda-tag-and-push: ecr-login
	@echo "$(GREEN)Tagging and pushing image to LocalStack ECR...$(NC)"
	docker tag $(IMAGE_NAME):$(IMAGE_TAG) $(ECR_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)
	docker push $(ECR_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)

.PHONY: lambda-push
lambda-push: ecr-create-repo lambda-tag-and-push
	@echo "$(GREEN)Image pushed successfully!$(NC)"

.PHONY: lambda-deploy
lambda-deploy: lambda-build lambda-push tflocal-apply
	@echo "$(GREEN)Lambda function deployed successfully!$(NC)"

.PHONY: lambda-update
lambda-update: lambda-build lambda-tag-and-push
	@echo "$(GREEN)Updating Lambda function with new image...$(NC)"
	aws --endpoint-url=$(LOCALSTACK_ENDPOINT) lambda update-function-code \
		--function-name $(FUNCTION_NAME) \
		--image-uri $(ECR_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG) \
		--region $(AWS_REGION)
	@echo "$(GREEN)Lambda function updated!$(NC)"

.PHONY: lambda-logs
lambda-logs:
	@echo "$(BLUE)Lambda Function Logs:$(NC)"
	aws --endpoint-url=$(LOCALSTACK_ENDPOINT) logs describe-log-groups \
		--log-group-name-prefix "/aws/lambda/$(FUNCTION_NAME)" \
		--region $(AWS_REGION) --query 'logGroups[0].logGroupName' --output text | \
	xargs -I {} aws --endpoint-url=$(LOCALSTACK_ENDPOINT) logs tail {} \
		--follow --region $(AWS_REGION)

# ============================================================================
# Testing and Validation
# ============================================================================

.PHONY: test-setup
test-setup:
	@echo "$(GREEN)Setting up test environment...$(NC)"
	@./scripts/setup-parameters.sh
	@echo "$(GREEN)Uploading test data to S3...$(NC)"
	aws --endpoint-url=$(LOCALSTACK_ENDPOINT) s3 cp test-data/sample-products.csv \
		s3://$(BUCKET_NAME)/user123/incoming/products.csv --region $(AWS_REGION)

.PHONY: test-s3-event
test-s3-event:
	@echo "$(GREEN)Testing S3 event processing...$(NC)"
	aws --endpoint-url=$(LOCALSTACK_ENDPOINT) lambda invoke \
		--function-name $(FUNCTION_NAME) \
		--payload file://test-data/s3-event.json \
		--region $(AWS_REGION) \
		response.json
	@echo "$(BLUE)Lambda Response:$(NC)"
	@cat response.json | jq .

.PHONY: test-api
test-api:
	@echo "$(GREEN)Testing API Gateway endpoints...$(NC)"
	@echo "$(YELLOW)Note: API Gateway testing will be implemented after core Lambda testing$(NC)"

.PHONY: test-full
test-full: test-setup test-s3-event
	@echo "$(GREEN)Full test suite completed!$(NC)"

# ============================================================================
# Utilities
# ============================================================================

.PHONY: cleanup
cleanup:
	@echo "$(YELLOW)Cleaning up LocalStack resources...$(NC)"
	-tflocal destroy -auto-approve
	-docker-compose down -v
	-docker system prune -f

.PHONY: reset
reset: cleanup
	@echo "$(YELLOW)Resetting LocalStack environment...$(NC)"
	-rm -rf tmp/localstack
	-rm -rf .terraform
	-rm terraform.tfstate*

.PHONY: dev-setup
dev-setup: localstack-start tflocal-init lambda-deploy test-setup
	@echo "$(GREEN)Development environment is ready!$(NC)"
	@echo "$(BLUE)Next steps:$(NC)"
	@echo "  - Run 'make test-s3-event' to test Lambda processing"
	@echo "  - Run 'make lambda-logs' to view function logs"
	@echo "  - Run 'make lambda-update' after code changes"
