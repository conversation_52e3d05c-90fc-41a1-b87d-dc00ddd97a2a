# AI rules
*.claude

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node_modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Build directories
build/
dist/
out/

# TypeScript
*.tsbuildinfo

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# AWS and Terraform
.terraform/
.terraform.lock.hcl
*.tfstate
*.tfstate.*
*.tfvars
*.auto.tfvars
.terraform.tfstate.lock.info
terraform.tfplan
terraform.tfstate.backup
*.tfplan
*.tfplan.*
.terraformrc
terraform.rc
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.terraform.d/
crash.log
crash.*.log
.terragrunt-cache/
terragrunt.hcl.backup
.tflint.hcl
.terraform-version

# Additional Terraform patterns
*.tfstate.lock.info
.terraform.lock.info
terraform.tfvars.backup
.terraform-docs.yml
.terraform-docs.yaml
.infracost/
.tfsec/
.checkov.yaml
.checkov.yml
.terrascan/
.terragrunt-source-map
.terragrunt-source/
.terragrunt-cache/
.terragrunt-debug.tfvars.json

# Terraform provider cache
.terraform/providers/
.terraform/modules/

# Terraform workspace files
terraform.tfstate.d/
.terraform.tfstate.d/

# Terraform plan output files
plan.out
plan.json
tfplan.out
tfplan.json

# Terraform sensitive files
terraform.tfvars.example
*.sensitive.tfvars
*.secret.tfvars

# Local testing override files (keep for development, ignore for production)
# Note: Remove the override.tf line below if you want to commit test configurations
# infra/iac/aws-ezychat-uat-tf/override.tf
# infra/iac/aws-ezychat-prod-tf/override.tf

# AWS credentials
.aws/
credentials
config

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# Kubernetes
*.kubeconfig
kubeconfig

# Local development
.local/
.vscode/settings.json
.vscode/launch.json

# Database
*.db
*.sqlite
*.sqlite3

# Redis
dump.rdb

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Application specific
uploads/
downloads/
media/
static/
storage/

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Certificate files
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Secrets and configuration
secrets.yml
secrets.yaml
config.local.yml
config.local.yaml

# Monitoring and logs
*.log.*
logs/
monitoring/
metrics/

# Lock files (keep only package-lock.json)
yarn.lock
pnpm-lock.yaml

# IDE specific files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Cloud provider specific
.gcp/
.azure/
.digitalocean/

# Package manager
.pnpm-debug.log*
.npm/
.yarn/

# Build artifacts
*.map
*.min.js
*.min.css