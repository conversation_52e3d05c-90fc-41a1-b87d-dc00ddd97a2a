version: '3.8'

services:
  localstack:
    container_name: ezychat-localstack
    image: localstack/localstack-pro:latest
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4510-4559:4510-4559"  # External services port range
    environment:
      # Core LocalStack configuration
      - DEBUG=1
      - PERSISTENCE=1
      - LAMBDA_EXECUTOR=docker
      - LAMBDA_REMOTE_DOCKER=false
      - LAMBDA_DOCKER_NETWORK=bridge
      
      # Services to enable
      - SERVICES=lambda,s3,ssm,apigateway,logs,sts,iam,ecr,cloudwatch,events,route53
      
      # LocalStack Pro configuration
      - LOCALSTACK_API_KEY=${LOCALSTACK_API_KEY}
      
      # AWS configuration
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      
      # Docker configuration
      - DOCKER_HOST=unix:///var/run/docker.sock
      
      # LocalStack specific settings
      - HOSTNAME_EXTERNAL=localhost
      - LS_LOG=debug
      
      # S3 configuration
      - S3_SKIP_SIGNATURE_VALIDATION=1
      
      # Lambda configuration
      - LAMBDA_STAY_OPEN_MODE=1
      - LAMBDA_IGNORE_ARCHITECTURE=1
      
    volumes:
      # Docker socket for Lambda container execution
      - "/var/run/docker.sock:/var/run/docker.sock"
      
      # Persistence volume
      - "./tmp/localstack:/var/lib/localstack"
      
      # Optional: Mount AWS credentials if needed
      - "${HOME}/.aws:/tmp/.aws"
    
    networks:
      - localstack-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  localstack-network:
    driver: bridge

volumes:
  localstack-data:
    driver: local
