# LocalStack Environment Configuration
# This configuration imports from the core infrastructure and adapts it for LocalStack

module "core" {
  source = "../core"

  env = "local"

  # Network Configuration for LocalStack Environment
  # Simplified networking for local development
  vpc_cidr             = "10.2.0.0/16" # Local: 10.2.0.0/16 (separate from UAT/Prod)
  public_subnet_count  = 2             # 2 public subnets for ALB
  private_subnet_count = 2             # 2 private subnets for ECS tasks

  # NAT Gateway Configuration - Disabled for LocalStack
  enable_nat_gateway   = false    # LocalStack: Disabled for simplicity
  nat_gateway_strategy = "single" # Not used when NAT Gateway is disabled
  use_private_subnets  = false    # LocalStack: Use public subnets for simplicity

  # Domain Configuration for LocalStack
  # Using localhost domains for local testing
  # Local: api.local.localhost (simplified for LocalStack)
  domain_name     = "localhost"
  api_subdomain   = "api"

  # Provider configuration for core module
  providers = {
    aws        = aws
    aws.master = aws.master
  }

  tags = {
    project     = "ezychat"
    environment = "localstack"
    purpose     = "local-development"
  }
}

# LocalStack-specific outputs from core module
output "core_lambda_platform" {
  description = "Core Lambda platform outputs"
  value       = module.core.lambda_platform
}

output "core_network" {
  description = "Core network outputs"
  value = {
    vpc_id             = module.core.vpc_id
    public_subnet_ids  = module.core.public_subnet_ids
    private_subnet_ids = module.core.private_subnet_ids
  }
}

# Note: LocalStack-specific resource outputs are defined in localstack_overrides.tf
