# LocalStack Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# LOCALSTACK CONFIGURATION
# =============================================================================

# LocalStack Pro API Key (required for ECR support)
# Get your API key from: https://app.localstack.cloud/
LOCALSTACK_API_KEY=your-localstack-pro-api-key-here

# LocalStack Configuration
LOCALSTACK_ENDPOINT=http://localhost:4566
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# =============================================================================
# LAMBDA FUNCTION CONFIGURATION
# =============================================================================

# Environment Settings
PYTHON_ENV=localstack
LOG_LEVEL=DEBUG
ENVIRONMENT=local

# Processing Limits
MAX_FILE_SIZE_BYTES=52428800
MAX_ROWS=100000
BATCH_SIZE=100

# Retry Configuration
RETRY_ATTEMPTS=3
RETRY_MIN_WAIT=1
RETRY_MAX_WAIT=60

# =============================================================================
# EXTERNAL SERVICES (for testing)
# =============================================================================

# OpenAI Configuration (use test API key)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-test-openai-api-key-here
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=512

# Supabase Configuration (use test project)
# Get these from your test Supabase project settings
SUPABASE_URL=https://your-test-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-test-service-role-key-here
SUPABASE_ANON_KEY=your-test-anonymous-key-here

# =============================================================================
# LOCALSTACK SPECIFIC SETTINGS
# =============================================================================

# S3 Configuration
DOCUMENT_BUCKET_NAME=ezychat-documents-local

# Lambda Configuration
LAMBDA_FUNCTION_NAME=document-ingestion-local

# ECR Configuration
ECR_REPOSITORY_NAME=lambda-document-ingestion

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Settings
DEBUG=1
VERBOSE_LOGGING=true

# Test Data Configuration
TEST_USER_ID=user123
TEST_CSV_FILE=sample-products.csv

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env.local:
#    cp .env.local.example .env.local

# 2. Fill in your LocalStack Pro API key and external service credentials

# 3. Source the environment file:
#    source .env.local

# 4. Start the development environment:
#    make dev-setup

# 5. Test the Lambda function:
#    make test-s3-event
