"""
Dify SDK Async Usage Examples

This script demonstrates how to use the async Dify Python SDK for 
high-performance concurrent operations.
"""

import asyncio
import os
import logging
import time
from typing import List

# Import the Dify SDK
from dify import AsyncDifyClient, DifyConfig
from dify.models.common import ResponseMode
from dify.exceptions import DifyError, DifyAPIError

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def basic_async_chat_example():
    """Demonstrate basic async chat functionality."""
    print("\n=== Basic Async Chat Example ===")
    
    async with AsyncDifyClient(
        api_key=os.getenv("DIFY_API_KEY", "your-api-key-here"),
        base_url=os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1")
    ) as client:
        try:
            # Send a chat message asynchronously
            response = await client.send_chat_message(
                query="Hello, how are you?",
                user="async-user-123",
                inputs={"context": "This is an async conversation"}
            )
            
            print(f"AI Response: {response.answer}")
            print(f"Conversation ID: {response.conversation_id}")
            
            # Continue the conversation
            follow_up = await client.send_chat_message(
                query="What's the weather like?",
                user="async-user-123",
                conversation_id=response.conversation_id
            )
            
            print(f"Follow-up Response: {follow_up.answer}")
            
        except DifyAPIError as e:
            print(f"API Error: {e}")
        except DifyError as e:
            print(f"SDK Error: {e}")


async def async_streaming_chat_example():
    """Demonstrate async streaming chat functionality."""
    print("\n=== Async Streaming Chat Example ===")
    
    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            # Send a streaming chat message
            stream = await client.send_chat_message(
                query="Tell me a story about async programming",
                user="async-user-123",
                response_mode=ResponseMode.STREAMING
            )
            
            print("Async streaming response:")
            async for event in stream:
                if hasattr(event, 'answer') and event.answer:
                    print(event.answer, end='', flush=True)
            
            print("\n[Async stream completed]")
            
        except DifyError as e:
            print(f"Error: {e}")


async def concurrent_requests_example():
    """Demonstrate concurrent request processing."""
    print("\n=== Concurrent Requests Example ===")
    
    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            # Define multiple queries to process concurrently
            queries = [
                "What is artificial intelligence?",
                "Explain machine learning",
                "What is deep learning?",
                "How does natural language processing work?",
                "What are neural networks?"
            ]
            
            # Create tasks for concurrent execution
            tasks = []
            for i, query in enumerate(queries):
                task = client.send_chat_message(
                    query=query,
                    user=f"concurrent-user-{i}"
                )
                tasks.append(task)
            
            # Execute all requests concurrently
            start_time = time.time()
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            print(f"Processed {len(queries)} requests concurrently in {end_time - start_time:.2f} seconds")
            
            # Process results
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    print(f"Query {i+1} failed: {response}")
                else:
                    print(f"Query {i+1}: {queries[i]}")
                    print(f"Response: {response.answer[:100]}...")
                    print()
            
        except Exception as e:
            print(f"Error in concurrent processing: {e}")


async def async_completion_example():
    """Demonstrate async text completion functionality."""
    print("\n=== Async Text Completion Example ===")
    
    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            # Create multiple completions concurrently
            completion_inputs = [
                {"text": "The future of artificial intelligence is"},
                {"text": "Machine learning algorithms can"},
                {"text": "Natural language processing enables"}
            ]
            
            tasks = []
            for inputs in completion_inputs:
                task = client.create_completion(
                    inputs=inputs,
                    user="async-completion-user"
                )
                tasks.append(task)
            
            # Execute completions concurrently
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    print(f"Completion {i+1} failed: {response}")
                else:
                    print(f"Input: {completion_inputs[i]['text']}")
                    print(f"Completion: {response.answer}")
                    print()
            
        except DifyError as e:
            print(f"Error: {e}")


async def async_streaming_with_timeout_example():
    """Demonstrate async streaming with timeout handling."""
    print("\n=== Async Streaming with Timeout Example ===")
    
    config = DifyConfig(
        api_key=os.getenv("DIFY_API_KEY", "your-api-key-here"),
        timeout=10.0,  # 10 second timeout
        read_timeout=15.0
    )
    
    async with AsyncDifyClient(config=config) as client:
        try:
            # Send a streaming request with timeout
            stream = await client.send_chat_message(
                query="Write a very long story about space exploration",
                user="timeout-user",
                response_mode=ResponseMode.STREAMING
            )
            
            print("Streaming with timeout:")
            
            # Process stream with timeout
            try:
                async with asyncio.timeout(20):  # 20 second overall timeout
                    async for event in stream:
                        if hasattr(event, 'answer') and event.answer:
                            print(event.answer, end='', flush=True)
            except asyncio.TimeoutError:
                print("\n[Stream timed out]")
            
        except DifyError as e:
            print(f"Error: {e}")


async def async_error_handling_example():
    """Demonstrate async error handling."""
    print("\n=== Async Error Handling Example ===")
    
    # Initialize client with invalid API key
    async with AsyncDifyClient(api_key="invalid-api-key") as client:
        try:
            response = await client.send_chat_message(
                query="This should fail",
                user="test-user"
            )
            print(f"Unexpected success: {response.answer}")
            
        except DifyAPIError as e:
            print(f"API Error (Status {e.status_code}): {e.message}")
            if e.request_id:
                print(f"Request ID: {e.request_id}")
                
        except DifyError as e:
            print(f"SDK Error: {e}")


async def async_health_check_example():
    """Demonstrate async health check."""
    print("\n=== Async Health Check Example ===")
    
    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            is_healthy = await client.health_check()
            print(f"API Health Status: {'Healthy' if is_healthy else 'Unhealthy'}")
            
        except Exception as e:
            print(f"Health check error: {e}")


async def batch_processing_example():
    """Demonstrate batch processing with rate limiting."""
    print("\n=== Batch Processing Example ===")
    
    config = DifyConfig(
        api_key=os.getenv("DIFY_API_KEY", "your-api-key-here"),
        rate_limit_rpm=30,  # Limit to 30 requests per minute
        max_retries=3
    )
    
    async with AsyncDifyClient(config=config) as client:
        try:
            # Simulate processing a batch of user queries
            user_queries = [
                ("user1", "What is Python?"),
                ("user2", "Explain async programming"),
                ("user3", "What are coroutines?"),
                ("user4", "How does asyncio work?"),
                ("user5", "What is event loop?"),
            ]
            
            # Process in smaller batches to respect rate limits
            batch_size = 3
            for i in range(0, len(user_queries), batch_size):
                batch = user_queries[i:i + batch_size]
                
                print(f"Processing batch {i//batch_size + 1}...")
                
                tasks = []
                for user_id, query in batch:
                    task = client.send_chat_message(
                        query=query,
                        user=user_id
                    )
                    tasks.append(task)
                
                # Process batch concurrently
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                
                for j, (response, (user_id, query)) in enumerate(zip(responses, batch)):
                    if isinstance(response, Exception):
                        print(f"  {user_id}: Error - {response}")
                    else:
                        print(f"  {user_id}: {response.answer[:50]}...")
                
                # Small delay between batches
                if i + batch_size < len(user_queries):
                    await asyncio.sleep(1)
            
        except Exception as e:
            print(f"Batch processing error: {e}")


async def async_workflow_example():
    """Demonstrate async workflow functionality."""
    print("\n=== Async Workflow Example ===")

    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            # Execute a workflow asynchronously
            response = await client.run_workflow(
                inputs={"data": "Process this information"},
                user="async-workflow-user-123"
            )

            print(f"Workflow executed: {response.workflow_run_id}")
            print(f"Status: {response.data.status}")

            # Get workflow runs
            runs = await client.get_workflow_runs(user="async-workflow-user-123")
            print(f"Total workflow runs: {len(runs.data)}")

        except DifyError as e:
            print(f"Workflow error: {e}")


async def async_file_management_example():
    """Demonstrate async file management functionality."""
    print("\n=== Async File Management Example ===")

    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            # List files asynchronously
            files = await client.list_files(user="async-file-user-123")
            print(f"Found {len(files.data)} files")

            # Note: File upload would require an actual file path
            # upload_response = await client.upload_file("./test.txt", "async-file-user-123")

        except DifyError as e:
            print(f"File management error: {e}")


async def async_conversation_example():
    """Demonstrate async conversation management functionality."""
    print("\n=== Async Conversation Management Example ===")

    async with AsyncDifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here")) as client:
        try:
            # Get conversations asynchronously
            conversations = await client.get_conversations(user="async-conv-user-123")
            print(f"Found {len(conversations.data)} conversations")

            if conversations.data:
                conv_id = conversations.data[0].id

                # Get conversation messages
                messages = await client.get_conversation_messages(
                    conversation_id=conv_id,
                    user="async-conv-user-123"
                )
                print(f"Conversation has {len(messages.data)} messages")

                # Rename conversation
                rename_response = await client.rename_conversation(
                    conversation_id=conv_id,
                    name="Updated Async Conversation",
                    user="async-conv-user-123"
                )
                print(f"Rename result: {rename_response.result}")

        except DifyError as e:
            print(f"Conversation management error: {e}")


async def main():
    """Run all async examples."""
    print("Dify SDK Async Examples")
    print("=" * 50)
    
    # Check if API key is available
    if not os.getenv("DIFY_API_KEY"):
        print("Warning: DIFY_API_KEY environment variable not set.")
        print("Some examples may fail. Please set your API key:")
        print("export DIFY_API_KEY='your-actual-api-key'")
        print()
    
    # Run examples
    try:
        await basic_async_chat_example()
        await async_streaming_chat_example()
        await concurrent_requests_example()
        await async_completion_example()
        await async_workflow_example()
        await async_file_management_example()
        await async_conversation_example()
        await async_streaming_with_timeout_example()
        await async_error_handling_example()
        await async_health_check_example()
        await batch_processing_example()
        
    except KeyboardInterrupt:
        print("\nAsync examples interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("Async examples completed!")


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
