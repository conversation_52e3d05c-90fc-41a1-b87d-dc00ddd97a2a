import 'reflect-metadata';
import { container } from 'tsyringe';

// Core services
import { ConfigService } from '../shared/config/config.service';
import { LoggerService } from '../shared/logging/logger.service';
import { HealthService } from '../application/services/health.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../shared/errors/error-handler';

// Domain services
import { SessionDomainService } from '../domain/services/SessionDomainService';

// Infrastructure services
import { SessionRepositoryDynamoDB } from '../infrastructure/database/SessionRepositoryDynamoDB';
import { BaileysAuthStateAdapter } from '../infrastructure/whatsapp/BaileysAuthStateAdapter';
import { WhatsAppService } from '../infrastructure/whatsapp/WhatsAppService';
import { BaileysConnectionManager } from '../infrastructure/whatsapp/BaileysConnectionManager';
import { QRCodeManager } from '../infrastructure/whatsapp/QRCodeManager';
import { MessageProcessor } from '../infrastructure/whatsapp/MessageProcessor';
import { RateLimiter } from '../infrastructure/whatsapp/RateLimiter';
import { BaileysEventEmitter } from '../infrastructure/whatsapp/BaileysEventEmitter';
import { ConnectionPool } from '../infrastructure/whatsapp/ConnectionPool';
import { HealthChecker } from '../infrastructure/whatsapp/HealthChecker';
import { WebhookManager } from '../infrastructure/whatsapp/WebhookManager';

// Use cases
import { StartSessionUseCase } from '../application/use-cases/StartSessionUseCase';
import { GetSessionStatusUseCase } from '../application/use-cases/GetSessionStatusUseCase';
import { TerminateSessionUseCase } from '../application/use-cases/TerminateSessionUseCase';
import { ListSessionsUseCase } from '../application/use-cases/ListSessionsUseCase';
import { RefreshQRCodeUseCase } from '../application/use-cases/RefreshQRCodeUseCase';
import { ProcessMessageUseCase } from '../application/use-cases/ProcessMessageUseCase';
import { HealthCheckUseCase } from '../application/use-cases/HealthCheckUseCase';

// Application services
import { SessionCleanupService } from '../application/services/SessionCleanupService';
import { MonitoringService } from '../application/services/MonitoringService';

// Controllers
import { HealthController } from '../api/controllers/health.controller';
import { SessionController } from '../api/controllers/session.controller';
import { AuthController } from '../api/controllers/AuthController';
import { QrAuthController } from '../api/controllers/QrAuthController';

// Auth-related imports
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { AuthService } from '../infrastructure/services/AuthService';
import { AuthTokenRepositoryDynamoDB } from '../infrastructure/repositories/AuthTokenRepositoryDynamoDB';
import { GenerateQrLinkUseCase } from '../application/use-cases/GenerateQrLinkUseCase';
import { DI_TOKENS } from './tokens';

/**
 * Dependency injection container setup
 * Registers all services and their dependencies
 */
export class DIContainer {
  static initialize(): void {
    // Register core services
    container.registerSingleton('IConfigService', ConfigService);
    container.registerSingleton('ILoggerService', LoggerService);

    // Register AWS services
    container.register(DI_TOKENS.DynamoDBClient, {
      useFactory: () => {
        return new DynamoDBClient({
          region: process.env['AWS_REGION'] || 'ap-southeast-1',
          endpoint: process.env['DYNAMODB_ENDPOINT'],
          credentials: process.env['DYNAMODB_ENDPOINT'] ? {
            accessKeyId: process.env['AWS_ACCESS_KEY_ID'] || 'fakeMyKeyId',
            secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'] || 'fakeSecretAccessKey'
          } : undefined
        });
      }
    });

    // Register domain services
    container.registerSingleton('ISessionDomainService', SessionDomainService);

    // Register infrastructure services
    container.registerSingleton('ISessionRepository', SessionRepositoryDynamoDB);
    container.registerSingleton('BaileysAuthStateAdapter', BaileysAuthStateAdapter);
    container.registerSingleton('BaileysConnectionManager', BaileysConnectionManager);
    container.registerSingleton('QRCodeManager', QRCodeManager);
    container.registerSingleton('MessageProcessor', MessageProcessor);
    container.registerSingleton('RateLimiter', RateLimiter);
    container.registerSingleton('BaileysEventEmitter', BaileysEventEmitter);
    container.registerSingleton('ConnectionPool', ConnectionPool);
    container.registerSingleton('HealthChecker', HealthChecker);
    container.registerSingleton('WebhookManager', WebhookManager);
    container.registerSingleton('IWhatsAppService', WhatsAppService);

    // Register auth services
    container.registerSingleton(DI_TOKENS.AuthService, AuthService);
    container.registerSingleton(DI_TOKENS.IAuthTokenRepository, AuthTokenRepositoryDynamoDB);

    // Register use cases
    container.registerSingleton('StartSessionUseCase', StartSessionUseCase);
    container.registerSingleton('GetSessionStatusUseCase', GetSessionStatusUseCase);
    container.registerSingleton('TerminateSessionUseCase', TerminateSessionUseCase);
    container.registerSingleton('ListSessionsUseCase', ListSessionsUseCase);
    container.registerSingleton('RefreshQRCodeUseCase', RefreshQRCodeUseCase);
    container.registerSingleton('ProcessMessageUseCase', ProcessMessageUseCase);
    container.registerSingleton('HealthCheckUseCase', HealthCheckUseCase);
    container.registerSingleton(DI_TOKENS.GenerateQrLinkUseCase, GenerateQrLinkUseCase);

    // Register application services
    container.registerSingleton('IHealthService', HealthService);
    container.registerSingleton('SessionCleanupService', SessionCleanupService);
    container.registerSingleton('MonitoringService', MonitoringService);

    // Register middleware
    container.registerSingleton('ErrorHandler', ErrorHandler);

    // Register controllers
    container.registerSingleton('HealthController', HealthController);
    container.registerSingleton('SessionController', SessionController);
    container.registerSingleton(DI_TOKENS.AuthController, AuthController);
    container.registerSingleton('QrAuthController', QrAuthController);
  }

  static getContainer(): typeof container {
    return container;
  }

  static resolve<T>(token: string): T {
    return container.resolve<T>(token);
  }

  static destroy(): void {
    // List of services that need cleanup (have destroy methods)
    const servicesToDestroy = [
      'SessionCleanupService',
      'MonitoringService',
      'ConnectionPool',
      'RateLimiter',
      'QRCodeManager',
      'MessageProcessor',
      'WebhookManager',
      'HealthChecker',
      'BaileysConnectionManager'
    ];

    // Call destroy on each service that has the method
    for (const serviceToken of servicesToDestroy) {
      try {
        const service = container.resolve(serviceToken) as any;
        if (service && typeof service.destroy === 'function') {
          service.destroy();
        }
      } catch (error: any) {
        // Service might not be registered or already destroyed, ignore
        console.warn(`Warning: Could not destroy service ${serviceToken}:`, error.message);
      }
    }

    // Clear all registrations
    container.clearInstances();
  }
}

// Initialize container on module load
DIContainer.initialize();
