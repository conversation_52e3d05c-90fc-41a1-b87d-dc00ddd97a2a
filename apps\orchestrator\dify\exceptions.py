"""
Dify SDK Exception Classes

Comprehensive exception hierarchy for handling various error conditions
that can occur when interacting with the Dify API.
"""

from typing import Optional, Dict, Any


class DifyError(Exception):
    """
    Base exception for all Dify SDK errors.
    
    This is the root exception class that all other Dify SDK exceptions
    inherit from. It provides a consistent interface for error handling.
    """
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.details:
            return f"{self.message} (Details: {self.details})"
        return self.message


class DifyAPIError(DifyError):
    """
    Exception raised for API-related errors (4xx, 5xx HTTP responses).
    
    This exception includes the HTTP status code and the full response
    data for detailed error analysis.
    """
    
    def __init__(
        self, 
        message: str, 
        status_code: int, 
        response_data: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ):
        super().__init__(message, {"status_code": status_code, "response_data": response_data})
        self.status_code = status_code
        self.response_data = response_data or {}
        self.request_id = request_id
    
    def __str__(self) -> str:
        base_msg = f"HTTP {self.status_code}: {self.message}"
        if self.request_id:
            base_msg += f" (Request ID: {self.request_id})"
        if self.response_data:
            base_msg += f" (Response: {self.response_data})"
        return base_msg


class DifyAuthenticationError(DifyAPIError):
    """
    Exception raised for authentication failures (401, 403).
    
    This typically indicates an invalid API key or insufficient permissions.
    """
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        status_code = kwargs.pop('status_code', 401)
        super().__init__(message, status_code=status_code, **kwargs)


class DifyRateLimitError(DifyAPIError):
    """
    Exception raised when rate limits are exceeded (429).
    
    Includes information about when the client can retry the request.
    """
    
    def __init__(
        self, 
        message: str = "Rate limit exceeded", 
        retry_after: Optional[int] = None,
        **kwargs
    ):
        status_code = kwargs.pop('status_code', 429)
        super().__init__(message, status_code=status_code, **kwargs)
        self.retry_after = retry_after
    
    def __str__(self) -> str:
        base_msg = super().__str__()
        if self.retry_after:
            base_msg += f" (Retry after: {self.retry_after} seconds)"
        return base_msg


class DifyValidationError(DifyError):
    """
    Exception raised for request validation errors.
    
    This occurs when the request data doesn't meet the API requirements
    or when response data doesn't match expected schemas.
    """
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.field = field
    
    def __str__(self) -> str:
        base_msg = super().__str__()
        if self.field:
            base_msg = f"Validation error in field '{self.field}': {base_msg}"
        return base_msg


class DifyConnectionError(DifyError):
    """
    Exception raised for network/connection errors.
    
    This includes DNS resolution failures, connection timeouts,
    and other network-related issues.
    """
    
    def __init__(self, message: str = "Connection error occurred", **kwargs):
        super().__init__(message, **kwargs)


class DifyTimeoutError(DifyError):
    """
    Exception raised when requests timeout.
    
    This occurs when the API doesn't respond within the specified timeout period.
    """
    
    def __init__(self, message: str = "Request timed out", timeout: Optional[float] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.timeout = timeout
    
    def __str__(self) -> str:
        base_msg = super().__str__()
        if self.timeout:
            base_msg += f" (Timeout: {self.timeout}s)"
        return base_msg


class DifyStreamingError(DifyError):
    """
    Exception raised for streaming-related errors.
    
    This occurs when there are issues with streaming responses,
    such as malformed SSE data or connection interruptions.
    """
    
    def __init__(self, message: str = "Streaming error occurred", **kwargs):
        super().__init__(message, **kwargs)


# Utility function to create appropriate exception from HTTP response
def create_exception_from_response(
    status_code: int, 
    response_data: Dict[str, Any], 
    request_id: Optional[str] = None
) -> DifyAPIError:
    """
    Create the appropriate exception based on HTTP status code and response data.
    
    Args:
        status_code: HTTP status code
        response_data: Response data from the API
        request_id: Optional request ID for tracking
        
    Returns:
        Appropriate DifyAPIError subclass instance
    """
    message = response_data.get('message', f'HTTP {status_code} error')
    
    if status_code == 401:
        return DifyAuthenticationError(message, status_code=status_code, 
                                     response_data=response_data, request_id=request_id)
    elif status_code == 403:
        return DifyAuthenticationError("Insufficient permissions", status_code=status_code,
                                     response_data=response_data, request_id=request_id)
    elif status_code == 429:
        retry_after = response_data.get('retry_after')
        return DifyRateLimitError(message, retry_after=retry_after, status_code=status_code,
                                response_data=response_data, request_id=request_id)
    else:
        return DifyAPIError(message, status_code=status_code, 
                          response_data=response_data, request_id=request_id)
