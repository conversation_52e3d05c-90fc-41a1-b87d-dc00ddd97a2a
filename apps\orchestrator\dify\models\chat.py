"""
Chat Models for Dify SDK

Pydantic models for chat-related API requests and responses.
"""

from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, field_validator

from .common import (
    BaseRequest,
    BaseResponse,
    ResponseMode,
    Usage,
    RetrieverResource,
    ConversationVariable,
    StreamEvent,
)


class ChatRequest(BaseRequest):
    """Request model for sending chat messages."""
    
    inputs: Dict[str, Any] = Field(
        default_factory=dict,
        description="Input variables for the chat application"
    )
    query: str = Field(
        description="User's chat message/query"
    )
    response_mode: ResponseMode = Field(
        default=ResponseMode.BLOCKING,
        description="Response mode: blocking or streaming"
    )
    conversation_id: Optional[str] = Field(
        default=None,
        description="Conversation ID for continuing an existing conversation"
    )
    user: str = Field(
        description="User identifier"
    )
    conversation_variables: Optional[List[ConversationVariable]] = Field(
        default=None,
        description="Session-specific variables for dynamic behavior"
    )
    files: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Files to include with the message"
    )
    auto_generate_name: bool = Field(
        default=True,
        description="Whether to auto-generate conversation name"
    )
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        """Ensure query is not empty."""
        if not v or not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()
    
    @field_validator('conversation_id')
    @classmethod
    def validate_conversation_id(cls, v):
        """Validate conversation ID format if provided."""
        if v is not None and not v.strip():
            raise ValueError("Conversation ID cannot be empty string")
        return v.strip() if v else None


class ChatMetadata(BaseModel):
    """Metadata included in chat responses."""
    
    usage: Optional[Usage] = Field(
        default=None,
        description="Token usage information"
    )
    retriever_resources: Optional[List[RetrieverResource]] = Field(
        default=None,
        description="Retrieved resources for RAG applications"
    )


class ChatResponse(BaseResponse):
    """Response model for chat messages."""
    
    event: str = Field(description="Event type")
    task_id: str = Field(description="Task identifier")
    id: str = Field(description="Message identifier")
    message_id: str = Field(description="Message identifier (same as id)")
    conversation_id: str = Field(description="Conversation identifier")
    mode: str = Field(description="Application mode")
    answer: str = Field(description="AI response/answer")
    metadata: Optional[ChatMetadata] = Field(
        default=None,
        description="Response metadata"
    )
    created_at: int = Field(description="Creation timestamp")
    
    @field_validator('answer')
    @classmethod
    def validate_answer(cls, v):
        """Ensure answer is a string."""
        return str(v) if v is not None else ""


class ChatStreamEvent(StreamEvent):
    """Streaming event for chat responses."""
    
    # Specific event types for chat streaming
    task_id: Optional[str] = Field(default=None, description="Task identifier")
    message_id: Optional[str] = Field(default=None, description="Message identifier")
    conversation_id: Optional[str] = Field(default=None, description="Conversation identifier")
    answer: Optional[str] = Field(default=None, description="Partial or complete answer")
    created_at: Optional[int] = Field(default=None, description="Creation timestamp")
    
    # For different event types
    class EventTypes:
        MESSAGE = "message"
        MESSAGE_END = "message_end"
        MESSAGE_REPLACE = "message_replace"
        ERROR = "error"
        PING = "ping"


class StopChatRequest(BaseRequest):
    """Request model for stopping chat message generation."""
    
    task_id: str = Field(description="Task ID of the chat message to stop")
    user: str = Field(description="User identifier")
    
    @field_validator('task_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class StopChatResponse(BaseResponse):
    """Response model for stopping chat message generation."""
    
    result: str = Field(description="Result of the stop operation")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"
        NOT_FOUND = "not_found"


class SuggestedQuestionsRequest(BaseRequest):
    """Request model for getting suggested questions."""
    
    message_id: str = Field(description="Message ID to get suggestions for")
    user: str = Field(description="User identifier")
    
    @field_validator('message_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class SuggestedQuestion(BaseModel):
    """Individual suggested question."""
    
    content: str = Field(description="Suggested question text")
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        """Ensure content is not empty."""
        if not v or not v.strip():
            raise ValueError("Question content cannot be empty")
        return v.strip()


class SuggestedQuestionsResponse(BaseResponse):
    """Response model for suggested questions."""
    
    result: str = Field(description="Result status")
    data: List[SuggestedQuestion] = Field(
        default_factory=list,
        description="List of suggested questions"
    )
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"


class ChatFeedbackRequest(BaseRequest):
    """Request model for providing feedback on chat messages."""
    
    message_id: str = Field(description="Message ID to provide feedback for")
    rating: str = Field(description="Feedback rating")
    content: Optional[str] = Field(
        default=None,
        description="Optional feedback content/comment"
    )
    user: str = Field(description="User identifier")
    
    @field_validator('message_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()
    
    @field_validator('rating')
    @classmethod
    def validate_rating(cls, v):
        """Validate rating value."""
        valid_ratings = {'like', 'dislike'}
        if v not in valid_ratings:
            raise ValueError(f"Rating must be one of: {', '.join(valid_ratings)}")
        return v
    
    class Ratings:
        LIKE = "like"
        DISLIKE = "dislike"


class ChatFeedbackResponse(BaseResponse):
    """Response model for chat feedback."""
    
    result: str = Field(description="Result status")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"


# Utility functions for chat models
def create_chat_request(
    query: str,
    user: str,
    conversation_id: Optional[str] = None,
    inputs: Optional[Dict[str, Any]] = None,
    response_mode: ResponseMode = ResponseMode.BLOCKING,
    **kwargs
) -> ChatRequest:
    """
    Create a ChatRequest with common parameters.
    
    Args:
        query: User's chat message
        user: User identifier
        conversation_id: Optional conversation ID
        inputs: Optional input variables
        response_mode: Response mode (blocking or streaming)
        **kwargs: Additional parameters
        
    Returns:
        ChatRequest instance
    """
    return ChatRequest(
        query=query,
        user=user,
        conversation_id=conversation_id,
        inputs=inputs or {},
        response_mode=response_mode,
        **kwargs
    )


def parse_chat_stream_event(event_data: str) -> Optional[ChatStreamEvent]:
    """
    Parse a streaming event from SSE data.
    
    Args:
        event_data: Raw event data string
        
    Returns:
        Parsed ChatStreamEvent or None if parsing fails
    """
    try:
        import json
        data = json.loads(event_data)
        return ChatStreamEvent(**data)
    except Exception:
        return None
