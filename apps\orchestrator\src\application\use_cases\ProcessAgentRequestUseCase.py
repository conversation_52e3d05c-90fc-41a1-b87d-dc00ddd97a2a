"""
Use case for processing agent requests.
"""

import time
import logging
from typing import Dict, Any
from ..interfaces import IDifyAgentService
from ...domain.entities import AgentRequest
from ...domain.services import ConfigurationService


class ProcessAgentRequestResult:
    """Result of processing an agent request."""

    def __init__(
        self,
        success: bool,
        conversation_id: str,
        response_text: str,
        tenant_id: str,
        metadata: Dict[str, Any],
        error_message: str = None,
    ):
        self.success = success
        self.conversation_id = conversation_id
        self.response_text = response_text
        self.tenant_id = tenant_id
        self.metadata = metadata
        self.error_message = error_message

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary format for API response."""
        result = {
            "success": self.success,
            "conversation_id": self.conversation_id,
            "response_text": self.response_text,
            "tenant_id": self.tenant_id,
            "metadata": self.metadata,
        }

        if self.error_message:
            result["error_message"] = self.error_message

        return result


class ProcessAgentRequestUseCase:
    """
    Use case for processing agent requests from WhatsApp.

    This use case orchestrates the entire flow:
    1. Load tenant configuration
    2. Process request through Dify agent
    3. Return formatted response
    """

    def __init__(
        self,
        dify_agent_service: IDifyAgentService,
        configuration_service: ConfigurationService,
        logger: logging.Logger = None,
    ):
        self.dify_agent_service = dify_agent_service
        self.configuration_service = configuration_service
        self.logger = logger or logging.getLogger(__name__)

    async def execute(self, request: AgentRequest) -> ProcessAgentRequestResult:
        """
        Execute the agent request processing use case.

        Args:
            request: The agent request from WhatsApp

        Returns:
            ProcessAgentRequestResult with the processing result
        """
        start_time = time.time()

        try:
            self.logger.info(f"Processing agent request for phone: {request.phone_number}")

            # Load tenant configuration (using default for now)
            tenant_config = self.configuration_service.get_tenant_config("default")

            # Process request through Dify agent
            agent_response = await self.dify_agent_service.process_agent_request(
                request, tenant_config
            )

            # Calculate total processing time
            processing_time_ms = int((time.time() - start_time) * 1000)

            # Prepare metadata
            metadata = {
                "processing_time_ms": processing_time_ms,
                "dify_response_id": agent_response.dify_response_id,
                "company_name": tenant_config.company_name,
                "agent_processing_time_ms": agent_response.processing_time_ms,
            }

            self.logger.info(
                f"Successfully processed request in {processing_time_ms}ms "
                f"for conversation: {agent_response.conversation_id}"
            )

            return ProcessAgentRequestResult(
                success=True,
                conversation_id=agent_response.conversation_id,
                response_text=agent_response.response_text,
                tenant_id=tenant_config.tenant_id,
                metadata=metadata,
            )

        except Exception as e:
            processing_time_ms = int((time.time() - start_time) * 1000)
            error_message = str(e)

            self.logger.error(f"Failed to process agent request: {error_message}", exc_info=True)

            return ProcessAgentRequestResult(
                success=False,
                conversation_id="",
                response_text="",
                tenant_id="",
                metadata={"processing_time_ms": processing_time_ms},
                error_message=error_message,
            )
