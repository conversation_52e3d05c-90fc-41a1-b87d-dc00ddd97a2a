"""
Retry Logic for Dify SDK

Implements exponential backoff retry logic for handling transient failures
when making API requests to the Dify service.
"""

import time
import random
import logging
from typing import Callable, Any, Set
from dataclasses import dataclass

import httpx

from ..config import RetryConfig
from ..exceptions import DifyRateLimitError, DifyConnectionError, DifyTimeoutError


logger = logging.getLogger(__name__)


class RetryHandler:
    """
    Handles retry logic with exponential backoff for API requests.
    
    This class implements a robust retry mechanism that can handle various
    types of failures including rate limits, server errors, and network issues.
    """
    
    def __init__(self, config: RetryConfig):
        """
        Initialize the retry handler.
        
        Args:
            config: RetryConfig instance with retry parameters
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function with retry logic.
        
        Args:
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Result of the function execution
            
        Raises:
            Exception: The last exception encountered if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                result = func(*args, **kwargs)
                
                # Check if the result is an HTTP response that should be retried
                if hasattr(result, 'status_code'):
                    if result.status_code in self.config.retry_on_status:
                        if attempt < self.config.max_retries:
                            self._handle_retry_response(result, attempt)
                            continue
                        else:
                            # Last attempt, let the response be processed normally
                            # The calling code will handle the error status
                            pass
                
                # Success or non-retryable response
                if attempt > 0:
                    self.logger.info(f"Request succeeded after {attempt} retries")
                return result
                
            except (httpx.ConnectError, httpx.TimeoutException, httpx.RequestError) as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    self.logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                    self._wait_before_retry(attempt)
                else:
                    self.logger.error(f"Request failed after {self.config.max_retries} retries: {e}")
                    break
            
            except Exception as e:
                # For non-network exceptions, don't retry
                self.logger.error(f"Non-retryable error: {e}")
                raise e
        
        # If we get here, all retries failed
        if last_exception:
            raise last_exception
    
    def _handle_retry_response(self, response: httpx.Response, attempt: int) -> None:
        """
        Handle a response that should be retried.
        
        Args:
            response: HTTP response object
            attempt: Current attempt number (0-based)
        """
        status_code = response.status_code
        
        if status_code == 429:
            # Rate limit - check for Retry-After header
            retry_after = response.headers.get('Retry-After')
            if retry_after:
                try:
                    wait_time = int(retry_after)
                    self.logger.info(f"Rate limited, waiting {wait_time} seconds (attempt {attempt + 1})")
                    time.sleep(wait_time)
                    return
                except ValueError:
                    pass
        
        # Default exponential backoff
        self.logger.warning(f"HTTP {status_code} error, retrying (attempt {attempt + 1})")
        self._wait_before_retry(attempt)
    
    def _wait_before_retry(self, attempt: int) -> None:
        """
        Wait before retrying with exponential backoff and jitter.
        
        Args:
            attempt: Current attempt number (0-based)
        """
        # Calculate base wait time with exponential backoff
        base_wait = self.config.backoff_factor ** attempt
        
        # Add jitter to avoid thundering herd
        jitter = random.uniform(0.1, 0.5)
        wait_time = base_wait + jitter
        
        # Cap the wait time to a reasonable maximum (e.g., 60 seconds)
        wait_time = min(wait_time, 60.0)
        
        self.logger.debug(f"Waiting {wait_time:.2f} seconds before retry")
        time.sleep(wait_time)
    
    def should_retry_exception(self, exception: Exception) -> bool:
        """
        Determine if an exception should trigger a retry.
        
        Args:
            exception: Exception that occurred
            
        Returns:
            True if the exception should trigger a retry
        """
        # Network-related exceptions should be retried
        if isinstance(exception, (httpx.ConnectError, httpx.TimeoutException, httpx.RequestError)):
            return True
        
        # Rate limit errors should be retried
        if isinstance(exception, DifyRateLimitError):
            return True
        
        # Connection and timeout errors should be retried
        if isinstance(exception, (DifyConnectionError, DifyTimeoutError)):
            return True
        
        return False
    
    def get_retry_delay(self, attempt: int, retry_after: int = None) -> float:
        """
        Calculate the delay before the next retry attempt.
        
        Args:
            attempt: Current attempt number (0-based)
            retry_after: Optional retry-after value from response headers
            
        Returns:
            Delay in seconds
        """
        if retry_after is not None:
            return float(retry_after)
        
        # Exponential backoff with jitter
        base_delay = self.config.backoff_factor ** attempt
        jitter = random.uniform(0.1, 0.5)
        return min(base_delay + jitter, 60.0)


# Decorator for automatic retry
def with_retry(config: RetryConfig = None):
    """
    Decorator to add retry logic to a function.
    
    Args:
        config: Optional RetryConfig. If not provided, uses default settings.
        
    Returns:
        Decorated function with retry logic
    """
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            retry_handler = RetryHandler(config)
            return retry_handler.execute_with_retry(func, *args, **kwargs)
        return wrapper
    return decorator
