# Dify Python SDK

A comprehensive Python SDK for interacting with the Dify API, designed for integration with the ezychat multi-tenant RAG architecture.

## Features

- **Clean, intuitive API**: Easy-to-use class-based interface
- **Type safety**: Full type hints and Pydantic model validation
- **Multi-tenant support**: Built-in factory pattern for tenant isolation
- **Comprehensive error handling**: Custom exception hierarchy with detailed error information
- **Retry logic**: Automatic retry with exponential backoff for transient failures
- **Rate limiting**: Client-side rate limiting to prevent API quota exhaustion
- **Streaming support**: Real-time streaming responses for chat and completion
- **Async support**: Asynchronous client for high-performance scenarios (coming soon)
- **Flexible configuration**: Environment variables, config files, and runtime overrides

## Installation

```bash
pip install -r requirements.txt
```

## Quick Start

### Basic Chat

```python
from dify import DifyClient

# Initialize client
client = DifyClient(api_key="your-api-key")

# Send a chat message
response = client.send_chat_message(
    query="Hello, how are you?",
    user="user-123"
)

print(response.answer)
client.close()
```

### Streaming Chat

```python
from dify import DifyClient
from dify.models.common import ResponseMode

client = DifyClient(api_key="your-api-key")

# Stream chat response
stream = client.send_chat_message(
    query="Tell me a story",
    user="user-123",
    response_mode=ResponseMode.STREAMING
)

for event in stream:
    if hasattr(event, 'answer') and event.answer:
        print(event.answer, end='', flush=True)

client.close()
```

### Text Completion

```python
from dify import DifyClient

client = DifyClient(api_key="your-api-key")

# Create text completion
response = client.create_completion(
    inputs={"text": "The future of AI is"},
    user="user-123"
)

print(response.answer)
client.close()
```

### Workflow Execution

```python
from dify import DifyClient

client = DifyClient(api_key="your-api-key")

# Execute a workflow
response = client.run_workflow(
    inputs={"input_text": "Process this data"},
    user="user-123"
)

print(f"Workflow result: {response.data.outputs}")
client.close()
```

### File Management

```python
from dify import DifyClient

client = DifyClient(api_key="your-api-key")

# Upload a file
upload_response = client.upload_file(
    file_path="./document.pdf",
    user="user-123"
)

print(f"File uploaded: {upload_response.id}")

# List files
files = client.list_files(user="user-123")
print(f"Total files: {len(files.data)}")

client.close()
```

### Conversation Management

```python
from dify import DifyClient

client = DifyClient(api_key="your-api-key")

# List conversations
conversations = client.get_conversations(user="user-123")
print(f"Total conversations: {len(conversations.data)}")

# Get conversation messages
if conversations.data:
    messages = client.get_conversation_messages(
        conversation_id=conversations.data[0].id,
        user="user-123"
    )
    print(f"Messages in conversation: {len(messages.data)}")

client.close()
```

### Multi-Tenant Usage

```python
from dify import DifyClientFactory, DifyConfig

# Configure factory
config = DifyConfig(
    api_key="default-api-key",
    base_url="https://api.dify.ai/v1"
)

factory = DifyClientFactory(config=config)

# Add tenant-specific configurations
factory.add_tenant_config(
    tenant_id="tenant-1",
    api_key="tenant-1-api-key",
    rate_limit_rpm=120
)

# Get tenant-specific client
client = factory.get_client(tenant_id="tenant-1")
response = client.send_chat_message(
    query="Hello from tenant 1",
    user="tenant1-user"
)

factory.clear_cache()
```

## Configuration

### Environment Variables

```bash
export DIFY_API_KEY="your-api-key"
export DIFY_BASE_URL="https://api.dify.ai/v1"
export DIFY_TIMEOUT="30.0"
export DIFY_MAX_RETRIES="3"
export DIFY_RATE_LIMIT_RPM="60"
export DIFY_ENABLE_STREAMING="true"
export DIFY_LOG_REQUESTS="false"
```

### Configuration Object

```python
from dify import DifyConfig, DifyClient

config = DifyConfig(
    api_key="your-api-key",
    base_url="https://api.dify.ai/v1",
    timeout=30.0,
    max_retries=3,
    rate_limit_rpm=60,
    enable_streaming=True,
    enable_retry=True,
    log_requests=True
)

client = DifyClient(config=config)
```

## Error Handling

```python
from dify import DifyClient
from dify.exceptions import (
    DifyAPIError,
    DifyAuthenticationError,
    DifyRateLimitError,
    DifyValidationError
)

client = DifyClient(api_key="your-api-key")

try:
    response = client.send_chat_message(
        query="Hello",
        user="user-123"
    )
    print(response.answer)
    
except DifyAuthenticationError as e:
    print(f"Authentication failed: {e}")
    
except DifyRateLimitError as e:
    print(f"Rate limit exceeded: {e}")
    if e.retry_after:
        print(f"Retry after {e.retry_after} seconds")
        
except DifyAPIError as e:
    print(f"API error {e.status_code}: {e.message}")
    
except DifyValidationError as e:
    print(f"Validation error: {e}")
    
finally:
    client.close()
```

## Advanced Features

### Context Manager

```python
from dify import DifyClient

with DifyClient(api_key="your-api-key") as client:
    response = client.send_chat_message(
        query="Hello",
        user="user-123"
    )
    print(response.answer)
# Client automatically closed
```

### Custom Retry Configuration

```python
from dify import DifyConfig, DifyClient

config = DifyConfig(
    api_key="your-api-key",
    max_retries=5,
    backoff_factor=2.0,
    retry_on_status={429, 500, 502, 503, 504}
)

client = DifyClient(config=config)
```

### Rate Limiting

```python
from dify import DifyConfig, DifyClient

config = DifyConfig(
    api_key="your-api-key",
    rate_limit_rpm=120,  # 120 requests per minute
    rate_limit_burst=10  # Allow burst of 10 requests
)

client = DifyClient(config=config)
```

## API Reference

### DifyClient

Main synchronous client for the Dify API.

#### Chat Methods
- `send_chat_message()` - Send a chat message
- `stop_chat_message()` - Stop chat message generation
- `get_suggested_questions()` - Get suggested follow-up questions
- `send_chat_feedback()` - Send feedback for a chat message

#### Completion Methods
- `create_completion()` - Create text completion
- `stop_completion()` - Stop completion generation
- `send_completion_feedback()` - Send feedback for completion

#### Workflow Methods
- `run_workflow()` - Execute a workflow
- `stop_workflow()` - Stop workflow execution
- `get_workflow_runs()` - List workflow runs

#### File Management Methods
- `upload_file()` - Upload a file
- `list_files()` - List uploaded files
- `delete_file()` - Delete a file
- `get_file_info()` - Get file information
- `download_file()` - Download a file

#### Conversation Management Methods
- `get_conversations()` - List conversations
- `get_conversation_messages()` - Get conversation messages
- `rename_conversation()` - Rename a conversation
- `delete_conversation()` - Delete a conversation
- `pin_conversation()` - Pin/unpin a conversation

### DifyClientFactory

Factory for creating and managing multi-tenant clients.

#### Methods
- `get_client()` - Get client for specific tenant
- `add_tenant_config()` - Add tenant configuration
- `remove_tenant_config()` - Remove tenant configuration
- `clear_cache()` - Clear cached clients
- `health_check()` - Perform health check

### Configuration Classes

- `DifyConfig` - Main configuration class
- `RetryConfig` - Retry behavior configuration
- `RateLimitConfig` - Rate limiting configuration

### Exception Classes

- `DifyError` - Base exception class
- `DifyAPIError` - API-related errors
- `DifyAuthenticationError` - Authentication failures
- `DifyRateLimitError` - Rate limit exceeded
- `DifyValidationError` - Request validation errors
- `DifyConnectionError` - Network/connection errors
- `DifyTimeoutError` - Request timeout errors

## Integration with EzyChat

This SDK is designed to integrate seamlessly with the ezychat multi-tenant RAG architecture:

```python
from dify import DifyClientFactory
from document_ingestion.config import config as ezychat_config

# Create factory with ezychat integration
factory = DifyClientFactory()

# Configure for each tenant based on their settings
for tenant_id in get_tenant_list():
    tenant_config = get_tenant_config(tenant_id)
    factory.add_tenant_config(
        tenant_id=tenant_id,
        api_key=tenant_config.dify_api_key,
        base_url=tenant_config.dify_base_url
    )

# Use in your application
def handle_chat_request(tenant_id: str, query: str, user_id: str):
    client = factory.get_client(tenant_id=tenant_id)
    response = client.send_chat_message(
        query=query,
        user=user_id
    )
    return response.answer
```

## Development Status

- ✅ Core SDK foundation
- ✅ Request/response models
- ✅ Chat endpoints
- ✅ Completion endpoints
- ✅ Workflow endpoints
- ✅ File management endpoints
- ✅ Conversation management endpoints
- ✅ Async client
- ✅ Advanced features (retry, rate limiting)

## Contributing

This SDK is part of the ezychat project. For contributions and issues, please follow the project's contribution guidelines.

## License

This SDK is part of the ezychat project and follows the same licensing terms.
