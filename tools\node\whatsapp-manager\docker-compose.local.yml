version: '3.8'

services:
  whatsapp-manager-build:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: whatsapp-manager:latest
    container_name: whatsapp-manager-build
    profiles:
      - build
    # This service is only used for building, not running

  whatsapp-manager-ecr:
    image: 611032973328.dkr.ecr.ap-southeast-5.amazonaws.com/whatsapp-manager:latest
    container_name: whatsapp-manager-ecr
    profiles:
      - ecr
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - PORT=3000
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      - CORS_ORIGINS=*
      - DYNAMODB_TABLE_NAME=WhatsAppSessions-uat
      - AWS_REGION=ap-southeast-5
      # JWT_SECRET should be provided via AWS Secrets Manager in production
      - JWT_SECRET=production-secret-key-change-me-in-aws-secrets
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

# Networks
networks:
  default:
    name: whatsapp-manager-network

# Volumes for persistent data (if needed in future)
volumes:
  app_data:
    driver: local
