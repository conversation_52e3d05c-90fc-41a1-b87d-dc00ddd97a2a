"""
Dify SDK Usage Examples

This script demonstrates how to use the Dify Python SDK for various operations
including chat, completion, and multi-tenant scenarios.
"""

import os
import logging
from typing import Dict, Any

# Import the Dify SDK
from dify import DifyClient, DifyConfig, DifyClientFactory
from dify.models.common import ResponseMode
from dify.exceptions import DifyError, DifyAPIError

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def basic_chat_example():
    """Demonstrate basic chat functionality."""
    print("\n=== Basic Chat Example ===")
    
    # Initialize client with API key
    client = DifyClient(
        api_key=os.getenv("DIFY_API_KEY", "your-api-key-here"),
        base_url=os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1")
    )
    
    try:
        # Send a chat message
        response = client.send_chat_message(
            query="Hello, how are you?",
            user="user-123",
            inputs={"context": "This is a friendly conversation"}
        )
        
        print(f"AI Response: {response.answer}")
        print(f"Conversation ID: {response.conversation_id}")
        
        # Continue the conversation
        follow_up = client.send_chat_message(
            query="What's the weather like?",
            user="user-123",
            conversation_id=response.conversation_id
        )
        
        print(f"Follow-up Response: {follow_up.answer}")
        
    except DifyAPIError as e:
        print(f"API Error: {e}")
    except DifyError as e:
        print(f"SDK Error: {e}")
    finally:
        client.close()


def streaming_chat_example():
    """Demonstrate streaming chat functionality."""
    print("\n=== Streaming Chat Example ===")
    
    client = DifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here"))
    
    try:
        # Send a streaming chat message
        stream = client.send_chat_message(
            query="Tell me a story about AI",
            user="user-123",
            response_mode=ResponseMode.STREAMING
        )
        
        print("Streaming response:")
        for event in stream:
            if hasattr(event, 'answer') and event.answer:
                print(event.answer, end='', flush=True)
        
        print("\n[Stream completed]")
        
    except DifyError as e:
        print(f"Error: {e}")
    finally:
        client.close()


def completion_example():
    """Demonstrate text completion functionality."""
    print("\n=== Text Completion Example ===")
    
    client = DifyClient(api_key=os.getenv("DIFY_API_KEY", "your-api-key-here"))
    
    try:
        # Create a text completion
        response = client.create_completion(
            inputs={
                "text": "The future of artificial intelligence is",
                "max_length": 100
            },
            user="user-123"
        )
        
        print(f"Completion: {response.answer}")
        
        # Usage information
        if response.metadata and response.metadata.usage:
            usage = response.metadata.usage
            print(f"Tokens used: {usage.total_tokens}")
            print(f"Cost: {usage.total_price} {usage.currency}")
        
    except DifyError as e:
        print(f"Error: {e}")
    finally:
        client.close()


def multi_tenant_example():
    """Demonstrate multi-tenant functionality using the factory."""
    print("\n=== Multi-Tenant Example ===")
    
    # Configure the factory with default settings
    config = DifyConfig(
        api_key=os.getenv("DIFY_API_KEY", "default-api-key"),
        base_url=os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1"),
        timeout=30.0,
        max_retries=3
    )
    
    factory = DifyClientFactory(config=config)
    
    # Add tenant-specific configurations
    factory.add_tenant_config(
        tenant_id="tenant-1",
        api_key="tenant-1-api-key",
        rate_limit_rpm=120  # Higher rate limit for premium tenant
    )
    
    factory.add_tenant_config(
        tenant_id="tenant-2", 
        api_key="tenant-2-api-key",
        rate_limit_rpm=60   # Standard rate limit
    )
    
    try:
        # Get client for tenant 1
        tenant1_client = factory.get_client(tenant_id="tenant-1")
        response1 = tenant1_client.send_chat_message(
            query="Hello from tenant 1",
            user="tenant1-user"
        )
        print(f"Tenant 1 response: {response1.answer}")
        
        # Get client for tenant 2
        tenant2_client = factory.get_client(tenant_id="tenant-2")
        response2 = tenant2_client.send_chat_message(
            query="Hello from tenant 2", 
            user="tenant2-user"
        )
        print(f"Tenant 2 response: {response2.answer}")
        
        # Get default client
        default_client = factory.get_client()
        response3 = default_client.send_chat_message(
            query="Hello from default tenant",
            user="default-user"
        )
        print(f"Default response: {response3.answer}")
        
    except DifyError as e:
        print(f"Error: {e}")
    finally:
        factory.clear_cache()


def error_handling_example():
    """Demonstrate error handling."""
    print("\n=== Error Handling Example ===")
    
    # Initialize client with invalid API key to demonstrate error handling
    client = DifyClient(api_key="invalid-api-key")
    
    try:
        response = client.send_chat_message(
            query="This should fail",
            user="test-user"
        )
        print(f"Unexpected success: {response.answer}")
        
    except DifyAPIError as e:
        print(f"API Error (Status {e.status_code}): {e.message}")
        if e.request_id:
            print(f"Request ID: {e.request_id}")
            
    except DifyError as e:
        print(f"SDK Error: {e}")
        
    finally:
        client.close()


def configuration_example():
    """Demonstrate different configuration options."""
    print("\n=== Configuration Example ===")
    
    # Create custom configuration
    custom_config = DifyConfig(
        api_key=os.getenv("DIFY_API_KEY", "your-api-key"),
        base_url=os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1"),
        timeout=60.0,
        max_retries=5,
        rate_limit_rpm=100,
        enable_streaming=True,
        enable_retry=True,
        log_requests=True,
        log_responses=False
    )
    
    client = DifyClient(config=custom_config)
    
    try:
        # The client will use the custom configuration
        response = client.send_chat_message(
            query="Testing custom configuration",
            user="config-test-user"
        )
        print(f"Response with custom config: {response.answer}")
        
    except DifyError as e:
        print(f"Error: {e}")
    finally:
        client.close()


def main():
    """Run all examples."""
    print("Dify SDK Examples")
    print("=" * 50)
    
    # Check if API key is available
    if not os.getenv("DIFY_API_KEY"):
        print("Warning: DIFY_API_KEY environment variable not set.")
        print("Some examples may fail. Please set your API key:")
        print("export DIFY_API_KEY='your-actual-api-key'")
        print()
    
    # Run examples
    try:
        basic_chat_example()
        streaming_chat_example()
        completion_example()
        multi_tenant_example()
        error_handling_example()
        configuration_example()
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("Examples completed!")


if __name__ == "__main__":
    main()
