"""
Multi-Tenant Dify SDK Integration Example

This script demonstrates how to use the Dify SDK in a multi-tenant
environment integrated with the ezychat architecture.
"""

import os
import logging
from typing import Dict, Any, List

# Import the Dify SDK integration
from dify.integration import (
    EzyChatDifyIntegration,
    setup_integration,
    chat_with_dify,
    complete_with_dify
)
from dify import DifyConfig
from dify.exceptions import DifyError

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_multi_tenant_environment():
    """Set up a multi-tenant environment with different configurations."""
    print("\n=== Setting Up Multi-Tenant Environment ===")
    
    # Create default configuration
    default_config = DifyConfig(
        api_key=os.getenv("DIFY_DEFAULT_API_KEY", "default-api-key"),
        base_url="https://api.dify.ai/v1",
        timeout=30.0,
        max_retries=3,
        rate_limit_rpm=60,
        enable_streaming=True,
        log_requests=True
    )
    
    # Define tenant configurations
    tenant_configs = {
        "enterprise-tenant": {
            "dify_api_key": os.getenv("DIFY_ENTERPRISE_API_KEY", "enterprise-api-key"),
            "rate_limit_rpm": 200,  # Higher rate limit for enterprise
            "max_retries": 5,
            "timeout": 60.0
        },
        "startup-tenant": {
            "dify_api_key": os.getenv("DIFY_STARTUP_API_KEY", "startup-api-key"),
            "rate_limit_rpm": 100,  # Medium rate limit
            "max_retries": 3,
            "timeout": 30.0
        },
        "free-tenant": {
            "dify_api_key": os.getenv("DIFY_FREE_API_KEY", "free-api-key"),
            "rate_limit_rpm": 30,   # Lower rate limit for free tier
            "max_retries": 2,
            "timeout": 20.0
        }
    }
    
    # Set up the integration
    integration = setup_integration(
        default_config=default_config,
        tenant_configs=tenant_configs
    )
    
    print(f"Set up integration with {len(tenant_configs)} tenants")
    return integration


def demonstrate_tenant_isolation():
    """Demonstrate how different tenants get isolated configurations."""
    print("\n=== Tenant Isolation Demo ===")
    
    integration = setup_multi_tenant_environment()
    
    # Test different tenants
    tenants = ["enterprise-tenant", "startup-tenant", "free-tenant"]
    
    for tenant_id in tenants:
        try:
            client = integration.get_client(tenant_id=tenant_id)
            print(f"Tenant '{tenant_id}' - Rate limit: {client.config.rate_limit_rpm} RPM")
            print(f"Tenant '{tenant_id}' - Timeout: {client.config.timeout}s")
            print(f"Tenant '{tenant_id}' - Max retries: {client.config.max_retries}")
            print()
            
        except Exception as e:
            print(f"Error getting client for tenant '{tenant_id}': {e}")


def simulate_multi_tenant_chat_requests():
    """Simulate chat requests from multiple tenants."""
    print("\n=== Multi-Tenant Chat Simulation ===")
    
    integration = setup_multi_tenant_environment()
    
    # Simulate different user requests from different tenants
    requests = [
        {
            "tenant_id": "enterprise-tenant",
            "user_id": "enterprise-user-1",
            "query": "What are the latest AI trends in enterprise software?",
            "context_data": {"department": "R&D", "role": "CTO"}
        },
        {
            "tenant_id": "startup-tenant", 
            "user_id": "startup-user-1",
            "query": "How can AI help our startup scale faster?",
            "context_data": {"company_size": "10-50", "industry": "fintech"}
        },
        {
            "tenant_id": "free-tenant",
            "user_id": "free-user-1", 
            "query": "What is machine learning?",
            "context_data": {"subscription": "free", "usage_limit": "basic"}
        }
    ]
    
    # Process requests
    for i, request in enumerate(requests, 1):
        print(f"Processing request {i} from tenant '{request['tenant_id']}'...")
        
        try:
            response = chat_with_dify(
                tenant_id=request["tenant_id"],
                user_id=request["user_id"],
                query=request["query"],
                context_data=request["context_data"]
            )
            
            if response["success"]:
                print(f"✓ Success: {response['answer'][:100]}...")
                print(f"  Conversation ID: {response['conversation_id']}")
            else:
                print(f"✗ Failed: {response['error']}")
                
        except Exception as e:
            print(f"✗ Exception: {e}")
        
        print()


def demonstrate_conversation_continuity():
    """Demonstrate conversation continuity within a tenant."""
    print("\n=== Conversation Continuity Demo ===")
    
    integration = setup_multi_tenant_environment()
    
    tenant_id = "enterprise-tenant"
    user_id = "enterprise-user-conversation"
    
    # Start a conversation
    print("Starting conversation...")
    response1 = chat_with_dify(
        tenant_id=tenant_id,
        user_id=user_id,
        query="Hello, I'm working on a new AI project.",
        context_data={"project_type": "machine_learning", "budget": "high"}
    )
    
    if response1["success"]:
        conversation_id = response1["conversation_id"]
        print(f"✓ Initial response: {response1['answer'][:100]}...")
        
        # Continue the conversation
        print("\nContinuing conversation...")
        response2 = chat_with_dify(
            tenant_id=tenant_id,
            user_id=user_id,
            query="What technologies should I consider?",
            conversation_id=conversation_id,
            context_data={"follow_up": True}
        )
        
        if response2["success"]:
            print(f"✓ Follow-up response: {response2['answer'][:100]}...")
            print(f"  Same conversation: {response2['conversation_id'] == conversation_id}")
        else:
            print(f"✗ Follow-up failed: {response2['error']}")
    else:
        print(f"✗ Initial request failed: {response1['error']}")


def demonstrate_completion_requests():
    """Demonstrate completion requests across tenants."""
    print("\n=== Multi-Tenant Completion Demo ===")
    
    integration = setup_multi_tenant_environment()
    
    completion_tasks = [
        {
            "tenant_id": "enterprise-tenant",
            "user_id": "enterprise-analyst",
            "inputs": {
                "text": "Based on market analysis, the key trends in AI adoption are",
                "context": "enterprise software market",
                "tone": "professional"
            }
        },
        {
            "tenant_id": "startup-tenant",
            "user_id": "startup-founder",
            "inputs": {
                "text": "Our startup's AI strategy should focus on",
                "context": "early-stage company",
                "tone": "innovative"
            }
        }
    ]
    
    for task in completion_tasks:
        print(f"Processing completion for tenant '{task['tenant_id']}'...")
        
        try:
            response = complete_with_dify(
                tenant_id=task["tenant_id"],
                user_id=task["user_id"],
                inputs=task["inputs"]
            )
            
            if response["success"]:
                print(f"✓ Completion: {response['answer'][:150]}...")
            else:
                print(f"✗ Failed: {response['error']}")
                
        except Exception as e:
            print(f"✗ Exception: {e}")
        
        print()


def health_check_all_tenants():
    """Perform health checks for all tenants."""
    print("\n=== Health Check All Tenants ===")
    
    integration = setup_multi_tenant_environment()
    
    health_results = integration.health_check_all_tenants()
    
    for tenant_id, is_healthy in health_results.items():
        status = "✓ Healthy" if is_healthy else "✗ Unhealthy"
        print(f"Tenant '{tenant_id}': {status}")


def show_tenant_statistics():
    """Show statistics about tenant configurations."""
    print("\n=== Tenant Statistics ===")
    
    integration = setup_multi_tenant_environment()
    
    stats = integration.get_tenant_stats()
    
    print(f"Total tenants: {stats['total_tenants']}")
    print(f"Tenant IDs: {', '.join(stats['tenant_ids'])}")
    print(f"Caching enabled: {stats['caching_enabled']}")
    print("\nDefault configuration:")
    for key, value in stats['default_config'].items():
        print(f"  {key}: {value}")


def demonstrate_error_handling():
    """Demonstrate error handling in multi-tenant environment."""
    print("\n=== Error Handling Demo ===")
    
    integration = setup_multi_tenant_environment()
    
    # Test with invalid tenant
    print("Testing with invalid tenant...")
    try:
        response = chat_with_dify(
            tenant_id="non-existent-tenant",
            user_id="test-user",
            query="This should use default config"
        )
        
        if response["success"]:
            print("✓ Fallback to default config worked")
        else:
            print(f"✗ Error: {response['error']}")
            
    except Exception as e:
        print(f"✗ Exception: {e}")
    
    # Test with invalid API key
    print("\nTesting with invalid API key...")
    integration.add_tenant(
        tenant_id="invalid-key-tenant",
        dify_api_key="invalid-key-12345"
    )
    
    response = chat_with_dify(
        tenant_id="invalid-key-tenant",
        user_id="test-user",
        query="This should fail"
    )
    
    if not response["success"]:
        print(f"✓ Properly handled error: {response['error_type']}")
    else:
        print("✗ Unexpected success with invalid key")


def main():
    """Run all multi-tenant examples."""
    print("Dify SDK Multi-Tenant Integration Examples")
    print("=" * 60)
    
    # Check if API keys are available
    if not any([
        os.getenv("DIFY_DEFAULT_API_KEY"),
        os.getenv("DIFY_ENTERPRISE_API_KEY"),
        os.getenv("DIFY_STARTUP_API_KEY"),
        os.getenv("DIFY_FREE_API_KEY")
    ]):
        print("Warning: No Dify API keys found in environment variables.")
        print("Set DIFY_DEFAULT_API_KEY, DIFY_ENTERPRISE_API_KEY, etc.")
        print("Some examples may fail without valid API keys.")
        print()
    
    try:
        demonstrate_tenant_isolation()
        simulate_multi_tenant_chat_requests()
        demonstrate_conversation_continuity()
        demonstrate_completion_requests()
        health_check_all_tenants()
        show_tenant_statistics()
        demonstrate_error_handling()
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        # Clean up
        try:
            from dify.integration import get_integration
            integration = get_integration()
            integration.cleanup()
            print("\nCleaned up integration resources.")
        except:
            pass
    
    print("\n" + "=" * 60)
    print("Multi-tenant examples completed!")


if __name__ == "__main__":
    main()
