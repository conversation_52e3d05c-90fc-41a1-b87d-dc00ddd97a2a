"""
Unit tests for Lambda handler.
"""

import json
import pytest
import sys
from pathlib import Path

# Ensure src is in path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Ensure current directory is in path for dify imports
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# Import fixtures
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import domain entities directly
from src.domain.entities import AgentRequest

# Import lambda utility functions that can be tested independently
try:
    from src.presentation.lambda_utils import parse_request_body, create_response
except ImportError:
    # If import fails, we'll skip the tests
    parse_request_body = None
    create_response = None


class TestLambdaHandler:
    """Test cases for Lambda handler."""

    @pytest.mark.skipif(create_response is None, reason="Lambda utils import failed")
    def test_create_response_success(self):
        """Test creating successful API Gateway response."""
        body = {"success": True, "message": "Test"}
        response = create_response(200, body)

        assert response["statusCode"] == 200
        assert "Content-Type" in response["headers"]
        assert response["headers"]["Content-Type"] == "application/json"
        assert "Access-Control-Allow-Origin" in response["headers"]

        parsed_body = json.loads(response["body"])
        assert parsed_body["success"] is True
        assert parsed_body["message"] == "Test"

    @pytest.mark.skipif(create_response is None, reason="Lambda utils import failed")
    def test_create_response_with_custom_headers(self):
        """Test creating response with custom headers."""
        body = {"test": "data"}
        custom_headers = {"X-Custom-Header": "custom-value"}
        response = create_response(200, body, custom_headers)

        assert response["headers"]["X-Custom-Header"] == "custom-value"
        assert response["headers"]["Content-Type"] == "application/json"

    @pytest.mark.skipif(parse_request_body is None, reason="Lambda utils import failed")
    def test_parse_request_body_valid(self, sample_api_gateway_event):
        """Test parsing valid request body."""
        request = parse_request_body(sample_api_gateway_event)

        assert isinstance(request, AgentRequest)
        assert request.phone_number == "+1234567890"
        assert request.message_content == "Hello, I need help"
        assert request.message_id == "test_msg_001"
        assert request.conversation_id is None

    @pytest.mark.skipif(parse_request_body is None, reason="Lambda utils import failed")
    def test_parse_request_body_with_conversation(self, sample_api_gateway_event_with_conversation):
        """Test parsing request body with conversation ID."""
        request = parse_request_body(sample_api_gateway_event_with_conversation)

        assert isinstance(request, AgentRequest)
        assert request.conversation_id == "test_conv_001"

    @pytest.mark.skipif(parse_request_body is None, reason="Lambda utils import failed")
    def test_parse_request_body_missing_field(self):
        """Test parsing request body with missing required field."""
        event = {
            "body": json.dumps(
                {
                    "phone_number": "+1234567890",
                    # Missing message_content and message_id
                }
            )
        }

        with pytest.raises(ValueError, match="Missing required field"):
            parse_request_body(event)

    @pytest.mark.skipif(parse_request_body is None, reason="Lambda utils import failed")
    def test_parse_request_body_invalid_json(self):
        """Test parsing request body with invalid JSON."""
        event = {"body": "invalid json"}

        with pytest.raises(ValueError, match="Invalid JSON"):
            parse_request_body(event)

    @pytest.mark.skipif(parse_request_body is None, reason="Lambda utils import failed")
    def test_parse_request_body_empty_body(self):
        """Test parsing request body with empty body."""
        event = {"body": "{}"}

        with pytest.raises(ValueError, match="Missing required field"):
            parse_request_body(event)

    def test_agent_request_creation(self):
        """Test AgentRequest creation and validation."""
        # Test valid request
        request = AgentRequest(
            phone_number="+1234567890", message_content="Test message", message_id="test_msg_001"
        )

        assert request.phone_number == "+1234567890"
        assert request.message_content == "Test message"
        assert request.message_id == "test_msg_001"
        assert request.conversation_id is None

        # Test user identifier generation
        user_id = request.get_user_identifier()
        assert user_id == "whatsapp_user_1234567890"
