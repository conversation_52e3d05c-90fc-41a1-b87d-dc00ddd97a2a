# LocalStack Provider Configuration
# This configures Terraform to use LocalStack endpoints instead of real AWS

terraform {
  required_version = ">= 1.7.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "> 5.0.0"
    }
  }

  # LocalStack backend configuration
  backend "local" {
    path = "terraform.tfstate"
  }
}

# Primary AWS provider for LocalStack
provider "aws" {
  region     = "us-east-1"
  access_key = "test"
  secret_key = "test"

  # LocalStack endpoints
  endpoints {
    apigateway     = "http://localhost:4566"
    apigatewayv2   = "http://localhost:4566"
    cloudformation = "http://localhost:4566"
    cloudwatch     = "http://localhost:4566"
    dynamodb       = "http://localhost:4566"
    ec2            = "http://localhost:4566"
    ecr            = "http://localhost:4566"
    ecs            = "http://localhost:4566"
    elbv2          = "http://localhost:4566"
    events         = "http://localhost:4566"
    iam            = "http://localhost:4566"
    lambda         = "http://localhost:4566"
    logs           = "http://localhost:4566"
    route53        = "http://localhost:4566"
    s3             = "http://s3.localhost.localstack.cloud:4566"
    secretsmanager = "http://localhost:4566"
    ssm            = "http://localhost:4566"
    sts            = "http://localhost:4566"
  }

  # Skip AWS-specific validations for LocalStack
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  # Default tags for all resources
  default_tags {
    tags = {
      Environment = "localstack"
      ManagedBy   = "terraform"
      Project     = "ezychat"
    }
  }
}

# Master AWS provider for LocalStack (same as primary for local development)
provider "aws" {
  alias      = "master"
  region     = "us-east-1"
  access_key = "test"
  secret_key = "test"

  # LocalStack endpoints (same as primary)
  endpoints {
    apigateway     = "http://localhost:4566"
    apigatewayv2   = "http://localhost:4566"
    cloudformation = "http://localhost:4566"
    cloudwatch     = "http://localhost:4566"
    dynamodb       = "http://localhost:4566"
    ec2            = "http://localhost:4566"
    ecr            = "http://localhost:4566"
    ecs            = "http://localhost:4566"
    elbv2          = "http://localhost:4566"
    events         = "http://localhost:4566"
    iam            = "http://localhost:4566"
    lambda         = "http://localhost:4566"
    logs           = "http://localhost:4566"
    route53        = "http://localhost:4566"
    s3             = "http://s3.localhost.localstack.cloud:4566"
    secretsmanager = "http://localhost:4566"
    ssm            = "http://localhost:4566"
    sts            = "http://localhost:4566"
  }

  # Skip AWS-specific validations for LocalStack
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  # Default tags for all resources
  default_tags {
    tags = {
      Environment = "localstack"
      ManagedBy   = "terraform"
      Project     = "ezychat"
    }
  }
}

# Variables for LocalStack configuration
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  type        = string
  default     = "http://localhost:4566"
}

variable "localstack_region" {
  description = "AWS region for LocalStack"
  type        = string
  default     = "us-east-1"
}
