"""
Domain service for managing tenant configurations.
"""

from typing import Dict
from ..entities import TenantConfig


class ConfigurationService:
    """
    Domain service for managing tenant configurations.

    Currently uses hardcoded configurations but designed to be extensible
    for future DynamoDB integration.
    """

    # Hardcoded tenant configurations as specified in requirements
    TENANT_CONFIGS = {
        "default": {
            "tenant_id": "demo_tenant_001",
            "company_name": "EzyChat Demo Company",
            "system_instructions": """You are a helpful sales assistant for EzyChat Demo Company.
            Help customers with product inquiries and provide excellent customer service.
            Be friendly, professional, and concise in your responses.""",
            "product_catalog": """
            - EzyChat Pro: $99/month - Advanced WhatsApp automation with AI-powered responses
            - EzyChat Basic: $49/month - Basic messaging features and contact management
            - EzyChat Enterprise: $299/month - Full enterprise solution with custom integrations
            """,
        }
    }

    def get_tenant_config(self, identifier: str = "default") -> TenantConfig:
        """
        Get tenant configuration by identifier.

        Args:
            identifier: Tenant identifier (defaults to "default")

        Returns:
            TenantConfig instance

        Raises:
            ValueError: If tenant configuration not found
        """
        config_data = self.TENANT_CONFIGS.get(identifier)

        if not config_data:
            # Fall back to default configuration
            config_data = self.TENANT_CONFIGS["default"]

        # Create TenantConfig instance
        return TenantConfig(
            tenant_id=config_data["tenant_id"],
            company_name=config_data["company_name"],
            system_instructions=config_data["system_instructions"],
            product_catalog=config_data["product_catalog"],
        )

    def get_available_tenants(self) -> Dict[str, str]:
        """
        Get list of available tenant identifiers and their company names.

        Returns:
            Dictionary mapping tenant identifiers to company names
        """
        return {
            identifier: config["company_name"] for identifier, config in self.TENANT_CONFIGS.items()
        }
