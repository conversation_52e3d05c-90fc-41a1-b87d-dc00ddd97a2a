"""
Test fixtures for hardcoded configurations.
"""

import pytest
import sys
from pathlib import Path

from domain.entities import TenantConfig, AgentRequest

# Ensure src is in path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Ensure current directory is in path for dify imports
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))


@pytest.fixture
def sample_tenant_config():
    """Sample tenant configuration for testing."""
    return TenantConfig(
        tenant_id="test_tenant_001",
        company_name="Test Company",
        system_instructions="You are a helpful test assistant.",
        product_catalog="Test Product A: $50, Test Product B: $100",
    )


@pytest.fixture
def sample_agent_request():
    """Sample agent request for testing."""
    return AgentRequest(
        phone_number="+1234567890",
        message_content="Hello, I need help with your products",
        conversation_id=None,
        message_id="test_msg_001",
    )


@pytest.fixture
def sample_agent_request_with_conversation():
    """Sample agent request with existing conversation for testing."""
    return AgentRequest(
        phone_number="+1234567890",
        message_content="Can you tell me more about pricing?",
        conversation_id="test_conv_001",
        message_id="test_msg_002",
    )


@pytest.fixture
def sample_api_gateway_event():
    """Sample API Gateway event for testing."""
    return {
        "httpMethod": "POST",
        "path": "/orchestrator",
        "headers": {"Content-Type": "application/json"},
        "body": """{
            "phone_number": "+1234567890",
            "message_content": "Hello, I need help",
            "message_id": "test_msg_001"
        }""",
    }


@pytest.fixture
def sample_api_gateway_event_with_conversation():
    """Sample API Gateway event with conversation ID for testing."""
    return {
        "httpMethod": "POST",
        "path": "/orchestrator",
        "headers": {"Content-Type": "application/json"},
        "body": """{
            "phone_number": "+1234567890",
            "message_content": "Tell me more about pricing",
            "conversation_id": "test_conv_001",
            "message_id": "test_msg_002"
        }""",
    }


@pytest.fixture
def sample_lambda_context():
    """Sample Lambda context for testing."""

    class MockLambdaContext:
        def __init__(self):
            self.function_name = "orchestrator-lambda"
            self.function_version = "$LATEST"
            self.invoked_function_arn = (
                "arn:aws:lambda:us-east-1:123456789012:function:orchestrator-lambda"
            )
            self.memory_limit_in_mb = 1024
            self.remaining_time_in_millis = 60000
            self.log_group_name = "/aws/lambda/orchestrator-lambda"
            self.log_stream_name = "2023/01/01/[$LATEST]abcdef123456"
            self.aws_request_id = "test-request-id"

    return MockLambdaContext()
