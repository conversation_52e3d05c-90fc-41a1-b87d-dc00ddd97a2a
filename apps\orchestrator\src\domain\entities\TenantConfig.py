"""
Domain entity representing tenant configuration.
"""

from typing import Dict, Any
from pydantic import BaseModel, Field


class TenantConfig(BaseModel):
    """
    Tenant configuration entity containing all settings for a specific tenant.

    This entity encapsulates tenant-specific configuration including
    company information and system instructions.
    """

    tenant_id: str = Field(description="Unique identifier for the tenant")
    company_name: str = Field(description="Company name for the tenant")
    system_instructions: str = Field(description="System instructions for the AI agent")
    product_catalog: str = Field(description="Product catalog information")

    def to_dify_inputs(self) -> Dict[str, Any]:
        """
        Convert tenant configuration to Dify API input format.

        Returns:
            Dictionary formatted for Dify API inputs
        """
        return {
            "system_instructions": self.system_instructions,
            "company_name": self.company_name,
            "product_catalog": self.product_catalog,
        }

    def get_user_identifier(self) -> str:
        """
        Get the user identifier for Dify API calls.

        Returns:
            Tenant ID formatted as user identifier
        """
        return self.tenant_id
