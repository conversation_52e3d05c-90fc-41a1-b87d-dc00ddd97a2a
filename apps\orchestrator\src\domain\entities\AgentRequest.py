"""
Domain entity representing an agent request from WhatsApp.
"""

from typing import Optional
from pydantic import BaseModel, Field, field_validator


class AgentRequest(BaseModel):
    """
    Agent request entity representing incoming WhatsApp message data.

    This entity encapsulates the simplified WhatsApp message format
    as specified in the requirements.
    """

    phone_number: str = Field(description="WhatsApp phone number in international format")
    message_content: str = Field(description="Content of the WhatsApp message")
    conversation_id: Optional[str] = Field(
        default=None, description="Existing conversation ID for continuing conversations"
    )
    message_id: str = Field(description="Unique identifier for this message")

    @field_validator("phone_number")
    @classmethod
    def validate_phone_number(cls, v):
        """Validate phone number format."""
        if not v or not v.strip():
            raise ValueError("Phone number cannot be empty")

        # Basic validation for international format
        if not v.startswith("+"):
            raise ValueError("Phone number must be in international format (start with +)")

        return v.strip()

    @field_validator("message_content")
    @classmethod
    def validate_message_content(cls, v):
        """Validate message content."""
        if not v or not v.strip():
            raise ValueError("Message content cannot be empty")

        return v.strip()

    @field_validator("message_id")
    @classmethod
    def validate_message_id(cls, v):
        """Validate message ID."""
        if not v or not v.strip():
            raise ValueError("Message ID cannot be empty")

        return v.strip()

    def get_user_identifier(self) -> str:
        """
        Generate a user identifier for Dify API calls.

        Returns:
            User identifier based on phone number
        """
        # Remove + and any non-numeric characters for cleaner identifier
        clean_phone = "".join(filter(str.isdigit, self.phone_number))
        return f"whatsapp_user_{clean_phone}"
