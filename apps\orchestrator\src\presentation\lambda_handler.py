"""
AWS Lambda handler for the orchestrator function.
"""

import async<PERSON>
from typing import Dict, Any

from ..infrastructure import get_container, wire_container, shutdown_container
from .lambda_utils import create_response, parse_request_body


# Global container for Lambda reuse
_container = None
_logger = None


def _get_container():
    """Get or initialize the global container."""
    global _container, _logger

    if _container is None:
        _container = get_container()
        wire_container()
        _logger = _container.logger()
        _logger.info("Container initialized for Lambda function")

    return _container


async def _process_request(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Process the agent request asynchronously.

    Args:
        event: API Gateway event
        context: Lambda context

    Returns:
        API Gateway response
    """
    container = _get_container()
    logger = container.logger()

    try:
        # Log request details
        logger.info(
            f"Processing request: {event.get('httpMethod', 'UNKNOWN')} {event.get('path', 'UNKNOWN')}"
        )

        # Handle OPTIONS request for CORS
        if event.get("httpMethod") == "OPTIONS":
            return create_response(200, {"message": "CORS preflight"})

        # Parse request body
        agent_request = parse_request_body(event)

        logger.info(f"Parsed agent request for phone: {agent_request.phone_number}")

        # Get use case and process request
        use_case = container.process_agent_request_use_case()
        result = await use_case.execute(agent_request)

        # Return response based on result
        if result.success:
            logger.info(f"Request processed successfully: {result.conversation_id}")
            return create_response(200, result.to_dict())
        else:
            logger.error(f"Request processing failed: {result.error_message}")
            return create_response(
                500,
                {
                    "success": False,
                    "error_message": result.error_message,
                    "conversation_id": "",
                    "response_text": "",
                    "tenant_id": "",
                    "metadata": result.metadata,
                },
            )

    except ValueError as e:
        logger.warning(f"Validation error: {e}")
        return create_response(
            400,
            {
                "success": False,
                "error_message": f"Invalid request: {e}",
                "conversation_id": "",
                "response_text": "",
                "tenant_id": "",
                "metadata": {},
            },
        )

    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        return create_response(
            500,
            {
                "success": False,
                "error_message": "Internal server error",
                "conversation_id": "",
                "response_text": "",
                "tenant_id": "",
                "metadata": {},
            },
        )


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler function.

    Args:
        event: API Gateway event
        context: Lambda context

    Returns:
        API Gateway response
    """
    # Run async processing
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        return loop.run_until_complete(_process_request(event, context))
    finally:
        loop.close()


# Lambda lifecycle hooks
def _lambda_init():
    """Initialize Lambda function (called on cold start)."""
    try:
        container = _get_container()
        logger = container.logger()
        logger.info("Lambda function initialized successfully")
    except Exception as e:
        print(f"Failed to initialize Lambda function: {e}")
        raise


def _lambda_shutdown():
    """Shutdown Lambda function resources."""
    try:
        shutdown_container()
        if _logger:
            _logger.info("Lambda function shutdown completed")
    except Exception as e:
        print(f"Error during Lambda shutdown: {e}")


# Initialize on module load
_lambda_init()
