"""
Test Dify library integration.
"""

import pytest
import sys
from pathlib import Path

# Ensure src is in path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Ensure current directory is in path for dify imports
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))


class TestDifyIntegration:
    """Test cases for Dify library integration."""

    def test_dify_client_import(self):
        """Test that Dify client can be imported."""
        from dify import DifyClient

        assert DifyClient is not None

    def test_dify_models_import(self):
        """Test that Dify models can be imported."""
        from dify.models.common import ResponseMode

        assert ResponseMode is not None
        assert hasattr(ResponseMode, "STREAMING")
        assert hasattr(ResponseMode, "BLOCKING")

    def test_dify_exceptions_import(self):
        """Test that Dify exceptions can be imported."""
        from dify.exceptions import DifyError, DifyAPIError, DifyTimeoutError, DifyValidationError

        assert DifyError is not None
        assert DifyAPIError is not None
        assert DifyTimeoutError is not None
        assert DifyValidationError is not None

        # Test exception hierarchy
        assert issubclass(DifyAPIError, DifyError)
        assert issubclass(DifyTimeoutError, DifyError)
        assert issubclass(DifyValidationError, DifyError)

    def test_dify_agent_service_import(self):
        """Test that DifyAgentService can be imported and instantiated."""
        try:
            from infrastructure.services import DifyAgentService

            # Test instantiation with valid mock API key
            service = DifyAgentService(api_key="app-1234567890abcdef1234567890abcdef")
            assert service is not None
            assert service.api_key == "app-1234567890abcdef1234567890abcdef"
        except ImportError:
            pytest.skip("DifyAgentService import failed due to relative import issues")

    def test_dify_client_instantiation(self):
        """Test that DifyClient can be instantiated."""
        from dify import DifyClient

        # Test instantiation with valid mock API key (Dify format)
        client = DifyClient(api_key="app-1234567890abcdef1234567890abcdef")
        assert client is not None
