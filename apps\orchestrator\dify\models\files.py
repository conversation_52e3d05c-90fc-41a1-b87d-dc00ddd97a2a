"""
File Models for Dify SDK

Pydantic models for file management API requests and responses.
"""

from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, field_validator
import mimetypes

from .common import (
    BaseRequest,
    BaseResponse,
    PaginationRequest,
    FileInfo,
)


class FileUploadRequest(BaseRequest):
    """Request model for file upload."""
    
    user: str = Field(description="User identifier")
    file_name: str = Field(description="Name of the file")
    file_content: Optional[bytes] = Field(
        default=None,
        description="File content (for direct upload)"
    )
    file_path: Optional[str] = Field(
        default=None,
        description="Path to the file (for file upload)"
    )
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()
    
    @field_validator('file_name')
    @classmethod
    def validate_file_name(cls, v):
        """Validate file name."""
        if not v or not v.strip():
            raise ValueError("File name cannot be empty")
        
        # Check for invalid characters
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        if any(char in v for char in invalid_chars):
            raise ValueError(f"File name contains invalid characters: {invalid_chars}")
        
        return v.strip()
    
    def get_mime_type(self) -> str:
        """Get MIME type based on file extension."""
        mime_type, _ = mimetypes.guess_type(self.file_name)
        return mime_type or 'application/octet-stream'


class FileUploadResponse(BaseResponse):
    """Response model for file upload."""
    
    id: str = Field(description="File ID")
    name: str = Field(description="File name")
    size: int = Field(description="File size in bytes")
    extension: str = Field(description="File extension")
    mime_type: str = Field(description="MIME type")
    created_by: str = Field(description="User who uploaded the file")
    created_at: int = Field(description="Upload timestamp")
    
    @field_validator('size')
    @classmethod
    def validate_size(cls, v):
        """Ensure file size is non-negative."""
        if v < 0:
            raise ValueError("File size cannot be negative")
        return v


class FileListRequest(PaginationRequest):
    """Request model for listing files."""
    
    user: str = Field(description="User identifier")
    search: Optional[str] = Field(
        default=None,
        description="Search query for file names"
    )
    extension: Optional[str] = Field(
        default=None,
        description="Filter by file extension"
    )
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()


class FileListResponse(BaseResponse):
    """Response model for file list."""
    
    data: List[FileInfo] = Field(
        default_factory=list,
        description="List of files"
    )
    total: int = Field(description="Total number of files")
    page: int = Field(description="Current page number")
    limit: int = Field(description="Number of items per page")
    has_more: bool = Field(description="Whether there are more items")


class FileDeleteRequest(BaseRequest):
    """Request model for file deletion."""
    
    file_id: str = Field(description="File ID to delete")
    user: str = Field(description="User identifier")
    
    @field_validator('file_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class FileDeleteResponse(BaseResponse):
    """Response model for file deletion."""
    
    result: str = Field(description="Result status")
    
    class Results:
        SUCCESS = "success"
        FAILED = "failed"
        NOT_FOUND = "not_found"


class FileDownloadRequest(BaseRequest):
    """Request model for file download."""
    
    file_id: str = Field(description="File ID to download")
    user: str = Field(description="User identifier")
    
    @field_validator('file_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class FileDownloadResponse(BaseResponse):
    """Response model for file download."""
    
    file_content: bytes = Field(description="File content")
    file_name: str = Field(description="File name")
    mime_type: str = Field(description="MIME type")
    size: int = Field(description="File size in bytes")


class FileInfoRequest(BaseRequest):
    """Request model for getting file information."""
    
    file_id: str = Field(description="File ID")
    user: str = Field(description="User identifier")
    
    @field_validator('file_id', 'user')
    @classmethod
    def validate_required_fields(cls, v):
        """Ensure required fields are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class FileInfoResponse(BaseResponse):
    """Response model for file information."""
    
    file: FileInfo = Field(description="File information")


class FileBatchDeleteRequest(BaseRequest):
    """Request model for batch file deletion."""
    
    file_ids: List[str] = Field(description="List of file IDs to delete")
    user: str = Field(description="User identifier")
    
    @field_validator('file_ids')
    @classmethod
    def validate_file_ids(cls, v):
        """Validate file IDs list."""
        if not v:
            raise ValueError("File IDs list cannot be empty")
        
        if len(v) > 100:
            raise ValueError("Cannot delete more than 100 files at once")
        
        # Check for duplicates
        if len(v) != len(set(v)):
            raise ValueError("Duplicate file IDs found")
        
        # Validate each ID
        for file_id in v:
            if not file_id or not file_id.strip():
                raise ValueError("File ID cannot be empty")
        
        return [file_id.strip() for file_id in v]
    
    @field_validator('user')
    @classmethod
    def validate_user(cls, v):
        """Ensure user ID is valid."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()


class FileBatchDeleteResponse(BaseResponse):
    """Response model for batch file deletion."""
    
    results: List[Dict[str, Any]] = Field(
        description="Results for each file deletion"
    )
    success_count: int = Field(description="Number of successfully deleted files")
    failure_count: int = Field(description="Number of failed deletions")


# File type validation
class SupportedFileTypes:
    """Supported file types for upload."""
    
    # Document types
    DOCUMENTS = {
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.rtf': 'application/rtf',
    }
    
    # Spreadsheet types
    SPREADSHEETS = {
        '.csv': 'text/csv',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }
    
    # Data types
    DATA = {
        '.json': 'application/json',
        '.xml': 'application/xml',
        '.yaml': 'application/x-yaml',
        '.yml': 'application/x-yaml',
    }
    
    # Image types (if supported)
    IMAGES = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.webp': 'image/webp',
    }
    
    @classmethod
    def get_all_supported(cls) -> Dict[str, str]:
        """Get all supported file types."""
        supported = {}
        supported.update(cls.DOCUMENTS)
        supported.update(cls.SPREADSHEETS)
        supported.update(cls.DATA)
        supported.update(cls.IMAGES)
        return supported
    
    @classmethod
    def is_supported(cls, file_extension: str) -> bool:
        """Check if a file extension is supported."""
        return file_extension.lower() in cls.get_all_supported()
    
    @classmethod
    def get_mime_type(cls, file_extension: str) -> Optional[str]:
        """Get MIME type for a file extension."""
        return cls.get_all_supported().get(file_extension.lower())


# Utility functions for file models
def create_file_upload_request(
    file_path: str,
    user: str,
    **kwargs
) -> FileUploadRequest:
    """
    Create a FileUploadRequest for file upload.
    
    Args:
        file_path: Path to the file to upload
        user: User identifier
        **kwargs: Additional parameters
        
    Returns:
        FileUploadRequest instance
    """
    import os
    file_name = os.path.basename(file_path)
    
    return FileUploadRequest(
        user=user,
        file_name=file_name,
        file_path=file_path,
        **kwargs
    )


def validate_file_for_upload(file_path: str, max_size: int = 100 * 1024 * 1024) -> None:
    """
    Validate a file for upload.
    
    Args:
        file_path: Path to the file
        max_size: Maximum file size in bytes (default: 100MB)
        
    Raises:
        ValueError: If the file is invalid
    """
    import os
    
    if not os.path.exists(file_path):
        raise ValueError(f"File does not exist: {file_path}")
    
    if not os.path.isfile(file_path):
        raise ValueError(f"Path is not a file: {file_path}")
    
    file_size = os.path.getsize(file_path)
    if file_size > max_size:
        max_mb = max_size / (1024 * 1024)
        raise ValueError(f"File size ({file_size} bytes) exceeds maximum ({max_mb:.1f}MB)")
    
    if file_size == 0:
        raise ValueError("File is empty")
    
    # Check file extension
    _, ext = os.path.splitext(file_path)
    if not SupportedFileTypes.is_supported(ext):
        supported_exts = ', '.join(SupportedFileTypes.get_all_supported().keys())
        raise ValueError(f"Unsupported file type: {ext}. Supported types: {supported_exts}")
