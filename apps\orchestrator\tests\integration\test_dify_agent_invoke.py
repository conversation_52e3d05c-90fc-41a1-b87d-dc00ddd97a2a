"""
Integration tests for Dify agent invoke functionality.

These tests verify that the Dify SDK can successfully communicate with the Dify API
and invoke agents with real test data. These are integration tests that require
a valid API key and network connectivity.
"""

import os
import pytest
import sys
import logging
from pathlib import Path
from dify import DifyClient, DifyConfig
from dify.models.common import ResponseMode
from dify.exceptions import DifyAPIError

# Add the dify package to the Python path
orchestrator_path = str(Path(__file__).parent.parent.parent)
sys.path.insert(0, orchestrator_path)

# Set up logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestDifyAgentInvoke:
    """Integration tests for Dify agent invoke functionality."""

    @pytest.fixture
    def test_data(self):
        """Provide test data for Dify agent invoke."""
        return {
            "inputs": {
                "company_name": "AnchorSprint",
                "system_instructions": "You are a helpful sales assistant for AnchorSprint. Always greet customers politely, recommend related products, and offer upselling opportunities. Answer in simple English suitable for customers in Malaysia.",
                "product_catalog": '[{"id":"P001","name":"Wireless Mouse","price":"RM59","stock":12,"promo":"Buy 1 Free 1"},{"id":"P002","name":"Mechanical Keyboard","price":"RM199","stock":5,"promo":"10% off this week"},{"id":"P003","name":"Laptop Stand","price":"RM89","stock":0,"promo":"Currently out of stock"}]',
            },
            "query": "Do you have any discounts on keyboards this week?",
            "response_mode": "streaming",
            "conversation_id": "",
            "user": "abc-123",
            "files": [],
        }

    @pytest.fixture
    def dify_client(self, integration_env_check):
        """Create a Dify client for testing."""
        api_key = integration_env_check  # This ensures we have a valid API key

        base_url = os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1")
        timeout = int(os.getenv("DIFY_TIMEOUT_SECONDS", "30"))

        config = DifyConfig(
            api_key=api_key,
            base_url=base_url,
            timeout=float(timeout),
            log_requests=True,
            log_responses=False,  # Avoid logging sensitive response data in tests
        )

        client = DifyClient(config=config)
        yield client
        client.close()

    @pytest.mark.integration
    def test_dify_agent_invoke_streaming(self, dify_client, test_data):
        """Test Dify agent invoke with streaming response mode."""
        logger.info("Testing Dify agent invoke with streaming mode")

        # Send chat message to Dify
        response = dify_client.send_chat_message(
            query=test_data["query"],
            user=test_data["user"],
            conversation_id=test_data["conversation_id"] if test_data["conversation_id"] else None,
            inputs=test_data["inputs"],
            response_mode=ResponseMode.STREAMING,
            files=test_data["files"] if test_data["files"] else None,
        )

        # Collect streaming response
        full_response = ""
        event_count = 0

        for event in response:
            event_count += 1
            if hasattr(event, "answer") and event.answer:
                full_response += event.answer
            elif hasattr(event, "data") and event.data and hasattr(event.data, "answer"):
                full_response += event.data.answer

        # Assertions
        assert event_count > 0, "Should receive at least one streaming event"
        assert len(full_response) > 0, "Should receive a non-empty response"
        assert (
            "keyboard" in full_response.lower() or "discount" in full_response.lower()
        ), "Response should be relevant to the query"

        logger.info(
            f"Received {event_count} events with total response length: {len(full_response)}"
        )

    @pytest.mark.integration
    def test_dify_agent_invoke_blocking_should_fail(self, dify_client, test_data):
        """Test that blocking mode fails for Agent Chat Apps (expected behavior)."""
        logger.info("Testing Dify agent invoke with blocking mode (expected to fail)")

        with pytest.raises(DifyAPIError) as exc_info:
            dify_client.send_chat_message(
                query=test_data["query"],
                user=test_data["user"],
                conversation_id=(
                    test_data["conversation_id"] if test_data["conversation_id"] else None
                ),
                inputs=test_data["inputs"],
                response_mode=ResponseMode.BLOCKING,
                files=test_data["files"] if test_data["files"] else None,
            )

        # Verify the error - should be 400 for validation error, not 401 for auth error
        if exc_info.value.status_code == 401:
            pytest.fail(
                f"Got authentication error (401) instead of validation error (400). "
                f"Check if DIFY_API_KEY in .env.local is valid. Error: {exc_info.value}"
            )

        assert (
            exc_info.value.status_code == 400
        ), f"Expected 400 error, got {exc_info.value.status_code}: {exc_info.value}"
        assert "Agent Chat App does not support blocking mode" in str(exc_info.value)

        logger.info("Blocking mode correctly failed as expected")

    @pytest.mark.integration
    def test_dify_agent_invoke_with_conversation_id(self, dify_client, test_data):
        """Test Dify agent invoke with conversation continuation."""
        logger.info("Testing conversation continuation")

        # First message
        first_response = dify_client.send_chat_message(
            query="Hello, I'm looking for computer accessories",
            user=test_data["user"],
            inputs=test_data["inputs"],
            response_mode=ResponseMode.STREAMING,
        )

        # Get conversation ID from first response
        conversation_id = None
        for event in first_response:
            if hasattr(event, "conversation_id") and event.conversation_id:
                conversation_id = event.conversation_id
                break
            elif hasattr(event, "data") and event.data and hasattr(event.data, "conversation_id"):
                conversation_id = event.data.conversation_id
                break

        assert conversation_id is not None, "Should receive a conversation ID"

        # Follow-up message with conversation ID
        follow_up_response = dify_client.send_chat_message(
            query=test_data["query"],
            user=test_data["user"],
            conversation_id=conversation_id,
            inputs=test_data["inputs"],
            response_mode=ResponseMode.STREAMING,
        )

        # Verify follow-up response
        follow_up_text = ""
        for event in follow_up_response:
            if hasattr(event, "answer") and event.answer:
                follow_up_text += event.answer

        assert len(follow_up_text) > 0, "Should receive a follow-up response"
        logger.info(f"Conversation continuation successful with ID: {conversation_id}")

    @pytest.mark.integration
    @pytest.mark.skip(reason="Dify SDK has a bug in exception handling - skipping until fixed")
    def test_dify_agent_invoke_invalid_api_key(self, test_data):
        """Test that invalid API key raises authentication error."""
        logger.info("Testing invalid API key handling")

        # Create client with invalid API key (but valid format)
        config = DifyConfig(
            api_key="app-invalid1234567890abcdef1234567890ab",
            base_url=os.getenv("DIFY_BASE_URL", "https://api.dify.ai/v1"),
            timeout=30.0,
        )

        client = DifyClient(config=config)

        try:
            # Expect any exception due to invalid API key
            with pytest.raises(Exception) as exc_info:
                response = client.send_chat_message(
                    query=test_data["query"],
                    user=test_data["user"],
                    inputs=test_data["inputs"],
                    response_mode=ResponseMode.STREAMING,
                )
                # Consume the iterator to trigger the API call
                list(response)

            # Verify it's an authentication-related error
            error_message = str(exc_info.value).lower()
            assert any(
                keyword in error_message
                for keyword in ["unauthorized", "invalid", "token", "authentication"]
            ), f"Should be an authentication error, got: {exc_info.value}"

        finally:
            client.close()

        logger.info("Invalid API key correctly handled")

    @pytest.mark.integration
    def test_dify_agent_invoke_missing_required_inputs(self, dify_client, test_data):
        """Test behavior when required inputs are missing."""
        logger.info("Testing missing required inputs")

        # Remove required inputs to test error handling
        incomplete_inputs = {
            "company_name": "AnchorSprint"
            # Missing system_instructions and product_catalog
        }

        # This might succeed or fail depending on the agent configuration
        # We'll test both scenarios
        try:
            response = dify_client.send_chat_message(
                query=test_data["query"],
                user=test_data["user"],
                inputs=incomplete_inputs,
                response_mode=ResponseMode.STREAMING,
            )

            # If it succeeds, just verify we get some response
            response_text = ""
            for event in response:
                if hasattr(event, "answer") and event.answer:
                    response_text += event.answer

            logger.info("Agent handled missing inputs gracefully")

        except DifyAPIError as e:
            # If it fails, verify it's a validation error, not an auth error
            if e.status_code == 401:
                pytest.fail(
                    f"Got authentication error (401) instead of validation error (400). "
                    f"Check if DIFY_API_KEY in .env.local is valid. Error: {e}"
                )

            assert e.status_code == 400, f"Should be a validation error, got {e.status_code}: {e}"
            logger.info(f"Agent correctly rejected missing inputs: {e}")

    @pytest.mark.unit
    def test_dify_client_configuration(self):
        """Test Dify client configuration without making API calls."""
        # Test with minimal configuration (use valid format API key)
        client = DifyClient(api_key="app-test1234567890abcdef1234567890ab")
        assert client.api_key == "app-test1234567890abcdef1234567890ab"
        assert client.base_url == "https://api.dify.ai/v1"  # Default
        client.close()

        # Test with custom configuration
        config = DifyConfig(
            api_key="app-test1234567890abcdef1234567890ab",
            base_url="https://custom.api.com/v1",
            timeout=60.0,
            log_requests=True,
        )
        client = DifyClient(config=config)
        assert client.api_key == "app-test1234567890abcdef1234567890ab"
        assert client.base_url == "https://custom.api.com/v1"
        client.close()

    @pytest.mark.unit
    def test_response_mode_enum(self):
        """Test ResponseMode enum values."""
        assert ResponseMode.STREAMING == "streaming"
        assert ResponseMode.BLOCKING == "blocking"

        # Test that we can create the enum values
        streaming_mode = ResponseMode.STREAMING
        blocking_mode = ResponseMode.BLOCKING

        assert streaming_mode != blocking_mode
