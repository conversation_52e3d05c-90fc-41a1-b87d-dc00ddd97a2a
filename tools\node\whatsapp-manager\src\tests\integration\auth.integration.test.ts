import request from 'supertest';
import { Application } from 'express';
import { IAuthTokenRepository } from '../../domain/repositories/IAuthTokenRepository';
import { DI_TOKENS } from '../../di/tokens';
import { TestDIContainer } from '../../di/test-container';

// Mock environment variables for testing
process.env['JWT_SECRET'] = 'test-secret-key-32-characters-long-minimum';
process.env['QR_TOKEN_EXPIRY_SEC'] = '300';
process.env['QR_TOKEN_MAX_ACTIVE'] = '5';
process.env['JWT_ISSUER'] = 'test-issuer';
process.env['JWT_AUDIENCE'] = 'test-audience';
process.env['QR_AUTH_BASE_URL'] = 'https://test.example.com';
process.env['DYNAMODB_ENDPOINT'] = 'http://localhost:8000';
process.env['AWS_REGION'] = 'ap-southeast-1';

// Import app after setting environment variables
import { App } from '../../app';

describe('Auth API Integration Tests', () => {
  let app: Application;
  let authTokenRepository: IAuthTokenRepository;
  let appInstance: App;

  beforeAll(async () => {
    // Clear any existing container registrations
    TestDIContainer.destroy();

    // Initialize test DI container
    TestDIContainer.initialize();

    // Create app instance
    appInstance = new App();
    app = appInstance.getApp();

    // Get services from test container
    authTokenRepository = TestDIContainer.resolve(DI_TOKENS.IAuthTokenRepository);

    // Clean up all tokens once at the beginning
    if (authTokenRepository && typeof (authTokenRepository as any).clear === 'function') {
      (authTokenRepository as any).clear();
    }
  });

  describe('POST /api/auth/qr-link', () => {
    it('should generate a QR authentication link successfully', async () => {
      const response = await request(app)
        .post('/api/auth/qr-link')
        .send({
          userId: 'test-user-123',
          expirySeconds: 600,
          metadata: { purpose: 'test' }
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('tokenId');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('qrAuthUrl');
      expect(response.body.data.userId).toBe('test-user-123');
      expect(response.body.data.qrAuthUrl).toContain('https://test.example.com');
      expect(response.body.data.qrAuthUrl).toContain('/auth/qr');
      expect(response.body.data.qrAuthUrl).toContain('token=');
    });

    it('should return 400 for invalid request', async () => {
      const response = await request(app)
        .post('/api/auth/qr-link')
        .send({
          userId: '', // Invalid empty user ID
          expirySeconds: 600
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid expiry seconds', async () => {
      const response = await request(app)
        .post('/api/auth/qr-link')
        .send({
          userId: 'test-user-123',
          expirySeconds: 0 // Invalid expiry
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should enforce max active tokens limit', async () => {
      const userId = 'test-user-max-tokens';
      
      // Create 5 tokens (the limit)
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/auth/qr-link')
          .send({ userId })
          .expect(201);
      }

      // 6th token should fail
      const response = await request(app)
        .post('/api/auth/qr-link')
        .send({ userId })
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('MAX_TOKENS_EXCEEDED');

      // Clean up
      await authTokenRepository.deleteByUserId(userId);
    });
  });

  describe('GET /api/auth/qr-link/:tokenId', () => {
    it('should get QR link details successfully', async () => {
      // First create a token
      const createResponse = await request(app)
        .post('/api/auth/qr-link')
        .send({ userId: 'test-user-123' })
        .expect(201);

      const tokenId = createResponse.body.data.tokenId;

      // Then get its details
      const response = await request(app)
        .get(`/api/auth/qr-link/${tokenId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokenId).toBe(tokenId);
      expect(response.body.data.userId).toBe('test-user-123');
      expect(response.body.data.isExpired).toBe(false);
      expect(response.body.data.isUsed).toBe(false);
    });

    it('should return 404 for non-existent token', async () => {
      const response = await request(app)
        .get('/api/auth/qr-link/non-existent-token')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('TOKEN_NOT_FOUND');
    });
  });

  describe('POST /api/auth/qr-link/:tokenId/validate', () => {
    it('should validate a valid token successfully', async () => {
      // Create a token
      const createResponse = await request(app)
        .post('/api/auth/qr-link')
        .send({ userId: 'test-user-123' })
        .expect(201);

      const tokenId = createResponse.body.data.tokenId;

      // Validate the token
      const response = await request(app)
        .post(`/api/auth/qr-link/${tokenId}/validate`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokenId).toBe(tokenId);
      expect(response.body.data.isValid).toBe(true);
    });

    it('should return 404 for non-existent token', async () => {
      const response = await request(app)
        .post('/api/auth/qr-link/non-existent-token/validate')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('TOKEN_NOT_FOUND');
    });
  });

  describe('POST /api/auth/qr-link/:tokenId/use', () => {
    it('should mark token as used successfully', async () => {
      // Create a token
      const createResponse = await request(app)
        .post('/api/auth/qr-link')
        .send({ userId: 'test-user-123' })
        .expect(201);

      const tokenId = createResponse.body.data.tokenId;

      // Use the token
      const response = await request(app)
        .post(`/api/auth/qr-link/${tokenId}/use`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokenId).toBe(tokenId);
      expect(response.body.data.usedAt).toBeDefined();
    });

    it('should return 409 when trying to use already used token', async () => {
      // Create a token
      const createResponse = await request(app)
        .post('/api/auth/qr-link')
        .send({ userId: 'test-user-123' })
        .expect(201);

      const tokenId = createResponse.body.data.tokenId;

      // Use the token first time
      await request(app)
        .post(`/api/auth/qr-link/${tokenId}/use`)
        .expect(200);

      // Try to use it again
      const response = await request(app)
        .post(`/api/auth/qr-link/${tokenId}/use`)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('TOKEN_USED');
    });
  });

  describe('GET /api/auth/users/:userId/tokens', () => {
    it('should get user tokens successfully', async () => {
      const userId = 'test-user-tokens';

      // Create multiple tokens
      await request(app)
        .post('/api/auth/qr-link')
        .send({ userId })
        .expect(201);

      await request(app)
        .post('/api/auth/qr-link')
        .send({ userId })
        .expect(201);

      // Get user tokens
      const response = await request(app)
        .get(`/api/auth/users/${userId}/tokens`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.userId).toBe(userId);
      expect(response.body.data.tokens).toHaveLength(2);
      expect(response.body.data.totalCount).toBe(2);
      expect(response.body.data.activeCount).toBe(2);

      // Clean up
      await authTokenRepository.deleteByUserId(userId);
    });

    it('should return empty array for user with no tokens', async () => {
      const response = await request(app)
        .get('/api/auth/users/user-no-tokens/tokens')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokens).toHaveLength(0);
      expect(response.body.data.totalCount).toBe(0);
    });
  });

  describe('DELETE /api/auth/users/:userId/tokens', () => {
    it('should revoke all user tokens successfully', async () => {
      const userId = 'test-user-revoke';

      // Create multiple tokens
      await request(app)
        .post('/api/auth/qr-link')
        .send({ userId })
        .expect(201);

      await request(app)
        .post('/api/auth/qr-link')
        .send({ userId })
        .expect(201);

      // Revoke all tokens
      const response = await request(app)
        .delete(`/api/auth/users/${userId}/tokens`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.revokedTokens).toBe(2);

      // Verify tokens are gone
      const getResponse = await request(app)
        .get(`/api/auth/users/${userId}/tokens`)
        .expect(200);

      expect(getResponse.body.data.tokens).toHaveLength(0);
    });
  });

  describe('DELETE /api/auth/qr-link/:tokenId', () => {
    it('should delete specific token successfully', async () => {
      // Create a token with a unique user ID
      const createResponse = await request(app)
        .post('/api/auth/qr-link')
        .send({ userId: 'test-user-delete' })
        .expect(201);

      const tokenId = createResponse.body.data.tokenId;

      // Delete the token
      const response = await request(app)
        .delete(`/api/auth/qr-link/${tokenId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tokenId).toBe(tokenId);

      // Verify token is gone
      await request(app)
        .get(`/api/auth/qr-link/${tokenId}`)
        .expect(404);
    });

    it('should return 404 for non-existent token', async () => {
      const response = await request(app)
        .delete('/api/auth/qr-link/non-existent-token')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('TOKEN_NOT_FOUND');
    });
  });

  describe('GET /api/auth/statistics', () => {
    it('should get auth statistics successfully', async () => {
      const response = await request(app)
        .get('/api/auth/statistics')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('totalTokensGenerated');
      expect(response.body.data).toHaveProperty('activeTokens');
      expect(response.body.data).toHaveProperty('expiredTokens');
      expect(response.body.data).toHaveProperty('usedTokens');
    });
  });

  describe('POST /api/auth/cleanup', () => {
    it('should cleanup expired tokens successfully', async () => {
      const response = await request(app)
        .post('/api/auth/cleanup')
        .send({ batchSize: 50 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('deletedTokens');
      expect(typeof response.body.data.deletedTokens).toBe('number');
    });

    it('should return 400 for invalid batch size', async () => {
      const response = await request(app)
        .post('/api/auth/cleanup')
        .send({ batchSize: 0 })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
