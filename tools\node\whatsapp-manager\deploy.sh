#!/bin/bash

# WhatsApp Personal API - Docker Build and Deploy to ECR Script
# This script builds the WhatsApp Personal API Docker image and pushes it to Amazon ECR

set -e

# Configuration
AWS_REGION="ap-southeast-5"  # Change to your AWS region if needed
ECR_REPOSITORY_NAME="whatsapp-manager"
IMAGE_TAG="latest"
AWS_PROFILE="ezychat-uat"  # Change to your AWS CLI profile if needed

aws sts get-caller-identity --profile ${AWS_PROFILE} --region ${AWS_REGION}

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="${SCRIPT_DIR}"

# Optional: Use git commit hash as tag for versioning
if command -v git &> /dev/null && [ -d .git ]; then
    GIT_COMMIT=$(git rev-parse --short HEAD)
    IMAGE_TAG="${IMAGE_TAG}-${GIT_COMMIT}"
fi

# Check for AWS CLI
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check for Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install it first."
    exit 1
fi

# Check Docker daemon
if ! docker info &> /dev/null; then
    echo "❌ Docker daemon is not running. Please start Docker first."
    exit 1
fi

# Check AWS SSO session
if ! aws sts get-caller-identity --profile ${AWS_PROFILE} &> /dev/null; then
    echo "❌ AWS SSO session is invalid or expired. Please login first."
    echo "   Run: aws sso login --profile ${AWS_PROFILE}"
    exit 1
fi

# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --profile ${AWS_PROFILE} --query Account --output text)

# ECR repository URI
ECR_REPOSITORY_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

echo ""
echo "🚀 === WhatsApp Personal API - Building and Deploying to Amazon ECR ==="
echo "📦 Repository: ${ECR_REPOSITORY_URI}"
echo "🏷️  Image tag: ${IMAGE_TAG}"
echo "🌍 AWS Region: ${AWS_REGION}"
echo "👤 AWS Account: ${AWS_ACCOUNT_ID}"
echo "👤 AWS Profile: ${AWS_PROFILE}"
echo ""

# Step 1: Create ECR repository if it doesn't exist
aws ecr describe-repositories --repository-names ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} --profile ${AWS_PROFILE} &> /dev/null || {
    echo "   Creating new ECR repository: ${ECR_REPOSITORY_NAME}"
    aws ecr create-repository --repository-name ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} --profile ${AWS_PROFILE} --image-scanning-configuration scanOnPush=true
}
echo "✅ ECR repository ready"

# Step 2: Authenticate Docker to ECR
aws ecr get-login-password --region ${AWS_REGION} --profile ${AWS_PROFILE} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
echo "✅ Docker authenticated with ECR"

# Step 3: Build the Docker image

echo "🔨 Building Docker image..."
cd "${PROJECT_ROOT}"  # Change to project root directory
docker build -f Dockerfile -t ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} .
echo "✅ Docker image built successfully"

# Step 4: Tag the image for ECR
docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_REPOSITORY_URI}:${IMAGE_TAG}
docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_REPOSITORY_URI}:latest
echo "✅ Image tagged for ECR"

# Step 5: Push the image to ECR
docker push ${ECR_REPOSITORY_URI}:${IMAGE_TAG}
docker push ${ECR_REPOSITORY_URI}:latest
echo "✅ Image pushed to ECR successfully"

echo ""
echo "🎉 === Deployment Complete ==="
echo "📦 Image URI: ${ECR_REPOSITORY_URI}:${IMAGE_TAG}"
echo "📦 Latest URI: ${ECR_REPOSITORY_URI}:latest"
echo ""
echo "🐳 To run this container locally:"
echo "docker run -p 3000:3000 \\"
echo "  -e NODE_ENV=production \\"
echo "  -e PORT=3000 \\"
echo "  -e HOST=0.0.0.0 \\"
echo "  -e AWS_PROFILE=${AWS_PROFILE} \\"
echo "  -e AWS_REGION=${AWS_REGION} \\"
echo "  -e ALLOWED_API_KEY_SECRET_NAME=dev/whatsapp-api/poc-wa-api-key-LUkANM \\"
echo "  -v \\$(pwd)/whatsapp-session:/app/whatsapp-session \\"
echo "  ${ECR_REPOSITORY_URI}:${IMAGE_TAG}"
echo ""
echo "☁️  To deploy to ECS, set these environment variables in your task definition:"
echo "  - NODE_ENV=production"
echo "  - PORT=3000"
echo "  - HOST=0.0.0.0"
echo "  - AWS_REGION=${AWS_REGION}"
echo "  - ALLOWED_API_KEY_SECRET_NAME=dev/whatsapp-api/poc-wa-api-key-LUkANM"
echo "  - SESSIONS_DIR=whatsapp-session"
echo ""
echo "📝 Note: Mount a persistent volume for the whatsapp-session directory to preserve WhatsApp authentication between container restarts."
