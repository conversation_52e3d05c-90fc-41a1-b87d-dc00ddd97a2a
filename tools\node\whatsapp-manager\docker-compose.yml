version: '3.8'

services:
  whatsapp-manager:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DOCKER_ENV=true
    env_file:
      - environments/docker.env
    volumes:
      - ./src:/app/src
      - /app/node_modules
    depends_on:
      dynamodb-local:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - whatsapp-network

  dynamodb-local:
    image: amazon/dynamodb-local:latest
    ports:
      - "8000:8000"
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-inMemory"]
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:8000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    networks:
      - whatsapp-network

networks:
  whatsapp-network:
    name: whatsapp-api-network
    driver: bridge

volumes:
  dynamodb-data:
