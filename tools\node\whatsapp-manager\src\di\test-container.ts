import 'reflect-metadata';
import { container } from 'tsyringe';

// Core services
import { ConfigService } from '../shared/config/config.service';
import { LoggerService } from '../shared/logging/logger.service';
import { HealthService } from '../application/services/health.service';
import { <PERSON>rror<PERSON>and<PERSON> } from '../shared/errors/error-handler';

// Domain services
import { SessionDomainService } from '../domain/services/SessionDomainService';

// Infrastructure services - Use Mock Repository for tests
import { MockSessionRepository } from '../infrastructure/database/MockSessionRepository';
import { MockAuthTokenRepository } from '../infrastructure/repositories/MockAuthTokenRepository';
import { BaileysAuthStateAdapter } from '../infrastructure/whatsapp/BaileysAuthStateAdapter';
import { WhatsAppService } from '../infrastructure/whatsapp/WhatsAppService';
import { MockBaileysConnectionManager } from '../infrastructure/whatsapp/MockBaileysConnectionManager';
import { QRCodeManager } from '../infrastructure/whatsapp/QRCodeManager';
import { MessageProcessor } from '../infrastructure/whatsapp/MessageProcessor';
import { RateLimiter } from '../infrastructure/whatsapp/RateLimiter';
import { BaileysEventEmitter } from '../infrastructure/whatsapp/BaileysEventEmitter';
import { ConnectionPool } from '../infrastructure/whatsapp/ConnectionPool';
import { MockHealthChecker } from '../infrastructure/whatsapp/MockHealthChecker';
import { WebhookManager } from '../infrastructure/whatsapp/WebhookManager';

// Auth services
import { AuthService } from '../infrastructure/services/AuthService';

// Use cases
import { StartSessionUseCase } from '../application/use-cases/StartSessionUseCase';
import { GetSessionStatusUseCase } from '../application/use-cases/GetSessionStatusUseCase';
import { TerminateSessionUseCase } from '../application/use-cases/TerminateSessionUseCase';
import { ListSessionsUseCase } from '../application/use-cases/ListSessionsUseCase';
import { RefreshQRCodeUseCase } from '../application/use-cases/RefreshQRCodeUseCase';
import { ProcessMessageUseCase } from '../application/use-cases/ProcessMessageUseCase';
import { HealthCheckUseCase } from '../application/use-cases/HealthCheckUseCase';
import { GenerateQrLinkUseCase } from '../application/use-cases/GenerateQrLinkUseCase';

// Application services
import { SessionCleanupService } from '../application/services/SessionCleanupService';
import { MonitoringService } from '../application/services/MonitoringService';

// Controllers
import { SessionController } from '../api/controllers/session.controller';
import { HealthController } from '../api/controllers/health.controller';
import { AuthController } from '../api/controllers/AuthController';

// DI Tokens
import { DI_TOKENS } from './tokens';

/**
 * Test-specific DI Container with mock implementations
 */
export class TestDIContainer {
  private static isInitialized = false;

  static initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Clear any existing registrations
    container.clearInstances();

    // Core services
    container.registerSingleton('IConfigService', ConfigService);
    container.registerSingleton('ILoggerService', LoggerService);
    container.registerSingleton('HealthService', HealthService);
    container.registerSingleton('ErrorHandler', ErrorHandler);

    // Domain services
    container.registerSingleton('SessionDomainService', SessionDomainService);

    // Infrastructure services - Use Mock Repository
    container.registerSingleton('ISessionRepository', MockSessionRepository);
    container.registerSingleton(DI_TOKENS.IAuthTokenRepository, MockAuthTokenRepository);
    container.registerSingleton('BaileysAuthStateAdapter', BaileysAuthStateAdapter);
    container.registerSingleton('IWhatsAppService', WhatsAppService);
    container.registerSingleton('BaileysConnectionManager', MockBaileysConnectionManager);
    container.registerSingleton('QRCodeManager', QRCodeManager);
    container.registerSingleton('MessageProcessor', MessageProcessor);
    container.registerSingleton('RateLimiter', RateLimiter);
    container.registerSingleton('BaileysEventEmitter', BaileysEventEmitter);
    container.registerSingleton('ConnectionPool', ConnectionPool);
    container.registerSingleton('HealthChecker', MockHealthChecker);
    container.registerSingleton('WebhookManager', WebhookManager);

    // Auth services - Use mock implementations
    container.registerSingleton(DI_TOKENS.AuthService, AuthService);

    // Use cases
    container.registerSingleton('StartSessionUseCase', StartSessionUseCase);
    container.registerSingleton('GetSessionStatusUseCase', GetSessionStatusUseCase);
    container.registerSingleton('TerminateSessionUseCase', TerminateSessionUseCase);
    container.registerSingleton('ListSessionsUseCase', ListSessionsUseCase);
    container.registerSingleton('RefreshQRCodeUseCase', RefreshQRCodeUseCase);
    container.registerSingleton('ProcessMessageUseCase', ProcessMessageUseCase);
    container.registerSingleton('HealthCheckUseCase', HealthCheckUseCase);
    container.registerSingleton(DI_TOKENS.GenerateQrLinkUseCase, GenerateQrLinkUseCase);

    // Application services
    container.registerSingleton('SessionCleanupService', SessionCleanupService);
    container.registerSingleton('MonitoringService', MonitoringService);

    // Controllers
    container.registerSingleton('SessionController', SessionController);
    container.registerSingleton('HealthController', HealthController);
    container.registerSingleton(DI_TOKENS.AuthController, AuthController);

    this.isInitialized = true;
  }

  static getContainer(): typeof container {
    return container;
  }

  static resolve<T>(token: string | symbol): T {
    return container.resolve<T>(token);
  }

  static destroy(): void {
    // List of services that need cleanup (have destroy methods)
    const servicesToDestroy = [
      'SessionCleanupService',
      'MonitoringService',
      'ConnectionPool',
      'RateLimiter',
      'QRCodeManager',
      'MessageProcessor',
      'WebhookManager',
      'HealthChecker',
      'BaileysConnectionManager'
    ];

    // Call destroy on each service that has the method
    for (const serviceToken of servicesToDestroy) {
      try {
        const service = container.resolve(serviceToken) as any;
        if (service && typeof service.destroy === 'function') {
          service.destroy();
        }
      } catch (error: any) {
        // Service might not be registered or already destroyed, ignore
        console.warn(`Warning: Could not destroy service ${serviceToken}:`, error.message);
      }
    }

    container.clearInstances();
    this.isInitialized = false;
  }

  static reset(): void {
    this.destroy();
    this.initialize();
  }
}
