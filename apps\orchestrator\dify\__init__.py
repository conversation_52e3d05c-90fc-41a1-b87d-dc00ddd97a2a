"""
Dify Python SDK

A comprehensive Python SDK for interacting with the Dify API, designed for
integration with the ezychat multi-tenant RAG architecture.

Features:
- Clean, intuitive class-based interface
- Comprehensive error handling with custom exceptions
- Type hints and proper documentation
- Support for both sync and async operations
- Built-in retry logic and rate limiting
- Multi-tenant support
- Integration with existing ezychat configuration

Example usage:
    from dify import DifyClient
    
    client = DifyClient(api_key="your-api-key")
    response = client.send_chat_message("Hello, how are you?")
    print(response.answer)
"""

from .client import DifyClient
from .async_client import AsyncDifyClient
from .config import DifyConfig
from .exceptions import (
    DifyError,
    DifyAPIError,
    DifyAuthenticationError,
    DifyRateLimitError,
    DifyValidationError,
    DifyConnectionError,
    DifyTimeoutError,
)
from .factory import DifyClientFactory

__version__ = "0.1.0"
__author__ = "EzyChat Team"
__email__ = "<EMAIL>"

__all__ = [
    # Main clients
    "DifyClient",
    "AsyncDifyClient",
    
    # Configuration
    "DifyConfig",
    
    # Factory for multi-tenant support
    "DifyClientFactory",
    
    # Exceptions
    "DifyError",
    "DifyAPIError", 
    "DifyAuthenticationError",
    "DifyRateLimitError",
    "DifyValidationError",
    "DifyConnectionError",
    "DifyTimeoutError",
    
    # Version info
    "__version__",
]
