"""
Unit tests for Lambda utility functions.
"""

import json
import pytest
import sys
from pathlib import Path

from src.domain.entities import AgentRequest

# Ensure src is in path for imports
src_path = Path(__file__).parent.parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Ensure current directory is in path for dify imports
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))


def create_response(status_code, body, headers=None):
    """Local copy of create_response function for testing."""
    default_headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
    }

    if headers:
        default_headers.update(headers)

    return {
        "statusCode": status_code,
        "headers": default_headers,
        "body": json.dumps(body, ensure_ascii=False),
    }


def parse_request_body(event):
    """Local copy of parse_request_body function for testing."""
    try:
        # Parse JSON body
        if isinstance(event.get("body"), str):
            body = json.loads(event["body"])
        else:
            body = event.get("body", {})

        # Validate required fields
        required_fields = ["phone_number", "message_content", "message_id"]
        for field in required_fields:
            if field not in body:
                raise ValueError(f"Missing required field: {field}")

        # Create AgentRequest entity
        return AgentRequest(
            phone_number=body["phone_number"],
            message_content=body["message_content"],
            conversation_id=body.get("conversation_id"),
            message_id=body["message_id"],
        )

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in request body: {e}")
    except Exception as e:
        raise ValueError(f"Failed to parse request: {e}")


class TestLambdaUtils:
    """Test cases for Lambda utility functions."""

    def test_create_response_success(self):
        """Test creating successful API Gateway response."""
        body = {"success": True, "message": "Test"}
        response = create_response(200, body)

        assert response["statusCode"] == 200
        assert "Content-Type" in response["headers"]
        assert response["headers"]["Content-Type"] == "application/json"
        assert "Access-Control-Allow-Origin" in response["headers"]

        parsed_body = json.loads(response["body"])
        assert parsed_body["success"] is True
        assert parsed_body["message"] == "Test"

    def test_create_response_with_custom_headers(self):
        """Test creating response with custom headers."""
        body = {"test": "data"}
        custom_headers = {"X-Custom-Header": "custom-value"}
        response = create_response(200, body, custom_headers)

        assert response["headers"]["X-Custom-Header"] == "custom-value"
        assert response["headers"]["Content-Type"] == "application/json"

    def test_parse_request_body_valid(self):
        """Test parsing valid request body."""
        event = {
            "body": json.dumps(
                {
                    "phone_number": "+1234567890",
                    "message_content": "Hello, I need help",
                    "message_id": "test_msg_001",
                }
            )
        }

        request = parse_request_body(event)

        assert isinstance(request, AgentRequest)
        assert request.phone_number == "+1234567890"
        assert request.message_content == "Hello, I need help"
        assert request.message_id == "test_msg_001"
        assert request.conversation_id is None

    def test_parse_request_body_with_conversation(self):
        """Test parsing request body with conversation ID."""
        event = {
            "body": json.dumps(
                {
                    "phone_number": "+1234567890",
                    "message_content": "Tell me more about pricing",
                    "conversation_id": "test_conv_001",
                    "message_id": "test_msg_002",
                }
            )
        }

        request = parse_request_body(event)

        assert isinstance(request, AgentRequest)
        assert request.conversation_id == "test_conv_001"

    def test_parse_request_body_missing_field(self):
        """Test parsing request body with missing required field."""
        event = {
            "body": json.dumps(
                {
                    "phone_number": "+1234567890",
                    # Missing message_content and message_id
                }
            )
        }

        with pytest.raises(ValueError, match="Missing required field"):
            parse_request_body(event)

    def test_parse_request_body_invalid_json(self):
        """Test parsing request body with invalid JSON."""
        event = {"body": "invalid json"}

        with pytest.raises(ValueError, match="Invalid JSON"):
            parse_request_body(event)

    def test_parse_request_body_empty_body(self):
        """Test parsing request body with empty body."""
        event = {"body": "{}"}

        with pytest.raises(ValueError, match="Missing required field"):
            parse_request_body(event)
