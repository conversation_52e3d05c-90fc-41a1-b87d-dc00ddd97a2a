#!/bin/bash

# Setup SSM Parameters for LocalStack
# This script populates the Parameter Store with required secrets and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
AWS_REGION="us-east-1"
PARAMETER_PREFIX="/ezychat/document-ingestion"

echo -e "${BLUE}Setting up SSM Parameters for LocalStack...${NC}"

# Check if LocalStack is running
if ! curl -s "$LOCALSTACK_ENDPOINT/_localstack/health" > /dev/null; then
    echo -e "${RED}Error: LocalStack is not running. Please start it first with 'make localstack-start'${NC}"
    exit 1
fi

# Load environment variables from .env.local if it exists
if [ -f ".env.local" ]; then
    echo -e "${GREEN}Loading environment variables from .env.local...${NC}"
    source .env.local
else
    echo -e "${YELLOW}Warning: .env.local not found. Using environment variables...${NC}"
fi

# Function to create SSM parameter
create_parameter() {
    local name="$1"
    local value="$2"
    local type="${3:-String}"
    local description="$4"
    
    echo -e "${GREEN}Creating parameter: ${name}${NC}"
    
    aws --endpoint-url="$LOCALSTACK_ENDPOINT" ssm put-parameter \
        --name "$name" \
        --value "$value" \
        --type "$type" \
        --description "$description" \
        --region "$AWS_REGION" \
        --overwrite > /dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Created: $name${NC}"
    else
        echo -e "${RED}✗ Failed to create: $name${NC}"
        exit 1
    fi
}

# OpenAI Configuration
echo -e "${BLUE}Setting up OpenAI parameters...${NC}"
if [ -n "$OPENAI_API_KEY" ]; then
    create_parameter \
        "$PARAMETER_PREFIX/openai/api-key" \
        "$OPENAI_API_KEY" \
        "SecureString" \
        "OpenAI API key for embeddings generation"
else
    echo -e "${YELLOW}Warning: OPENAI_API_KEY not set${NC}"
fi

create_parameter \
    "$PARAMETER_PREFIX/openai/model" \
    "${OPENAI_EMBEDDING_MODEL:-text-embedding-3-small}" \
    "String" \
    "OpenAI embedding model"

create_parameter \
    "$PARAMETER_PREFIX/openai/dimensions" \
    "${OPENAI_EMBEDDING_DIMENSIONS:-512}" \
    "String" \
    "OpenAI embedding dimensions"

# Supabase Configuration
echo -e "${BLUE}Setting up Supabase parameters...${NC}"
if [ -n "$SUPABASE_URL" ]; then
    create_parameter \
        "$PARAMETER_PREFIX/supabase/url" \
        "$SUPABASE_URL" \
        "String" \
        "Supabase project URL"
else
    echo -e "${YELLOW}Warning: SUPABASE_URL not set${NC}"
fi

if [ -n "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    create_parameter \
        "$PARAMETER_PREFIX/supabase/service-role-key" \
        "$SUPABASE_SERVICE_ROLE_KEY" \
        "SecureString" \
        "Supabase service role key"
else
    echo -e "${YELLOW}Warning: SUPABASE_SERVICE_ROLE_KEY not set${NC}"
fi

if [ -n "$SUPABASE_ANON_KEY" ]; then
    create_parameter \
        "$PARAMETER_PREFIX/supabase/anon-key" \
        "$SUPABASE_ANON_KEY" \
        "SecureString" \
        "Supabase anonymous key"
else
    echo -e "${YELLOW}Warning: SUPABASE_ANON_KEY not set${NC}"
fi

# Processing Configuration
echo -e "${BLUE}Setting up processing parameters...${NC}"
create_parameter \
    "$PARAMETER_PREFIX/processing/max-file-size" \
    "${MAX_FILE_SIZE_BYTES:-52428800}" \
    "String" \
    "Maximum file size in bytes"

create_parameter \
    "$PARAMETER_PREFIX/processing/max-rows" \
    "${MAX_ROWS:-100000}" \
    "String" \
    "Maximum number of rows to process"

create_parameter \
    "$PARAMETER_PREFIX/processing/batch-size" \
    "${BATCH_SIZE:-100}" \
    "String" \
    "Batch size for processing"

# Retry Configuration
echo -e "${BLUE}Setting up retry parameters...${NC}"
create_parameter \
    "$PARAMETER_PREFIX/retry/attempts" \
    "${RETRY_ATTEMPTS:-3}" \
    "String" \
    "Number of retry attempts"

create_parameter \
    "$PARAMETER_PREFIX/retry/min-wait" \
    "${RETRY_MIN_WAIT:-1}" \
    "String" \
    "Minimum wait time between retries"

create_parameter \
    "$PARAMETER_PREFIX/retry/max-wait" \
    "${RETRY_MAX_WAIT:-60}" \
    "String" \
    "Maximum wait time between retries"

# Verify parameters were created
echo -e "${BLUE}Verifying created parameters...${NC}"
aws --endpoint-url="$LOCALSTACK_ENDPOINT" ssm describe-parameters \
    --parameter-filters "Key=Name,Option=BeginsWith,Values=$PARAMETER_PREFIX" \
    --region "$AWS_REGION" \
    --query 'Parameters[].Name' \
    --output table

echo -e "${GREEN}✓ SSM Parameters setup completed successfully!${NC}"
echo -e "${BLUE}Parameters are available at: $PARAMETER_PREFIX/*${NC}"
